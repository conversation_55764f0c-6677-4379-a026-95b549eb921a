{"train_metrics": [{"accuracy": 0.9560381355932204, "precision": 0.9172932330827067, "recall": 0.6288659793814433, "f1_score": 0.7461773700305809, "auc_roc": 0.9389411841672854, "loss": 0.01952900584408287, "time": 122.93236947059631}, {"accuracy": 0.9682203389830508, "precision": 0.9328968903436988, "recall": 0.741222366710013, "f1_score": 0.8260869565217391, "auc_roc": 0.9769617380865151, "loss": 0.01251836471512156, "time": 125.74680590629578}, {"accuracy": 0.973781779661017, "precision": 0.9243323442136498, "recall": 0.8090909090909091, "f1_score": 0.8628808864265928, "auc_roc": 0.9831445537653146, "loss": 0.010784804292212603, "time": 121.47904920578003}, {"accuracy": 0.9711334745762712, "precision": 0.9199395770392749, "recall": 0.7868217054263565, "f1_score": 0.8481894150417827, "auc_roc": 0.9822189398288886, "loss": 0.012012418185881639, "time": 122.41324615478516}, {"accuracy": 0.9770921610169492, "precision": 0.9288762446657184, "recall": 0.8414948453608248, "f1_score": 0.883029073698445, "auc_roc": 0.9916200028298787, "loss": 0.008108986135161781, "time": 121.76779747009277}, {"accuracy": 0.9751059322033898, "precision": 0.9271137026239067, "recall": 0.8217054263565892, "f1_score": 0.8712328767123287, "auc_roc": 0.9872979765055356, "loss": 0.009869189445576552, "time": 123.08873796463013}, {"accuracy": 0.9794756355932204, "precision": 0.9426111908177905, "recall": 0.8510362694300518, "f1_score": 0.8944860449285228, "auc_roc": 0.9925977616274626, "loss": 0.007611013055084494, "time": 124.84052038192749}, {"accuracy": 0.9785487288135594, "precision": 0.9355300859598854, "recall": 0.8480519480519481, "f1_score": 0.8896457765667576, "auc_roc": 0.9912631603135879, "loss": 0.008709978996933895, "time": 124.625803232193}, {"accuracy": 0.9790783898305084, "precision": 0.9425287356321839, "recall": 0.8475452196382429, "f1_score": 0.8925170068027211, "auc_roc": 0.9923136336361065, "loss": 0.007763797227861517, "time": 125.77152156829834}, {"accuracy": 0.9810646186440678, "precision": 0.9438990182328191, "recall": 0.8672680412371134, "f1_score": 0.9039623908663533, "auc_roc": 0.9910063870056841, "loss": 0.0076402836988317, "time": 125.62845778465271}, {"accuracy": 0.981197033898305, "precision": 0.9460992907801419, "recall": 0.8651102464332037, "f1_score": 0.9037940379403794, "auc_roc": 0.9906740451834694, "loss": 0.007437245182345732, "time": 126.00056409835815}, {"accuracy": 0.9833156779661016, "precision": 0.9563994374120957, "recall": 0.8774193548387097, "f1_score": 0.9152086137281292, "auc_roc": 0.9941551833288114, "loss": 0.006511269275383171, "time": 125.01258969306946}, {"accuracy": 0.9833156779661016, "precision": 0.9484679665738162, "recall": 0.8844155844155844, "f1_score": 0.9153225806451614, "auc_roc": 0.9928945796167856, "loss": 0.006908495965713667, "time": 122.8218629360199}, {"accuracy": 0.9817266949152542, "precision": 0.9479606188466948, "recall": 0.8696774193548387, "f1_score": 0.9071332436069985, "auc_roc": 0.9931841570397026, "loss": 0.007014572296796714, "time": 121.8956549167633}, {"accuracy": 0.9831832627118644, "precision": 0.9475138121546961, "recall": 0.8851612903225806, "f1_score": 0.9152768512341561, "auc_roc": 0.9941046328425843, "loss": 0.006420970430708159, "time": 125.01485061645508}], "val_metrics": [{"accuracy": 0.9579872984855886, "precision": 0.948905109489051, "recall": 0.6220095693779905, "f1_score": 0.7514450867052024, "auc_roc": 0.9926290278074253, "loss": 0.017645709145619453}, {"accuracy": 0.9272105520273571, "precision": 0.5857142857142857, "recall": 0.9808612440191388, "f1_score": 0.7334525939177102, "auc_roc": 0.9906232591073093, "loss": 0.049752120101486816}, {"accuracy": 0.9643380556912555, "precision": 0.9657534246575342, "recall": 0.6746411483253588, "f1_score": 0.7943661971830985, "auc_roc": 0.986430799027443, "loss": 0.015338077157568396}, {"accuracy": 0.9804592085979482, "precision": 0.9720670391061452, "recall": 0.8325358851674641, "f1_score": 0.8969072164948454, "auc_roc": 0.9968175830812565, "loss": 0.006097280984070165}, {"accuracy": 0.7840742550073277, "precision": 0.3204930662557781, "recall": 0.9952153110047847, "f1_score": 0.48484848484848486, "auc_roc": 0.9919261627210771, "loss": 0.07829564756294781}, {"accuracy": 0.9848558866634098, "precision": 0.8973214285714286, "recall": 0.9617224880382775, "f1_score": 0.9284064665127021, "auc_roc": 0.9975438770038163, "loss": 0.007258792492207531}, {"accuracy": 0.9741084513922814, "precision": 0.812, "recall": 0.9712918660287081, "f1_score": 0.8845315904139434, "auc_roc": 0.9970687922695254, "loss": 0.017562331902486527}, {"accuracy": 0.9858329262335125, "precision": 0.9591836734693877, "recall": 0.8995215311004785, "f1_score": 0.928395061728395, "auc_roc": 0.9957307454014401, "loss": 0.00593644400770104}, {"accuracy": 0.979970688812897, "precision": 0.8589743589743589, "recall": 0.9617224880382775, "f1_score": 0.90744920993228, "auc_roc": 0.9967368837565276, "loss": 0.009628743096607367}, {"accuracy": 0.9741084513922814, "precision": 0.9936708860759493, "recall": 0.7511961722488039, "f1_score": 0.8555858310626704, "auc_roc": 0.9962552910121778, "loss": 0.007511712382781866}, {"accuracy": 0.9824132877381534, "precision": 0.8810572687224669, "recall": 0.9569377990430622, "f1_score": 0.9174311926605505, "auc_roc": 0.9979695008616606, "loss": 0.007481561308525572}, {"accuracy": 0.9877870053737177, "precision": 0.9259259259259259, "recall": 0.9569377990430622, "f1_score": 0.9411764705882353, "auc_roc": 0.9973252078658412, "loss": 0.006561809607963913}, {"accuracy": 0.9829018075232047, "precision": 0.9887640449438202, "recall": 0.8421052631578947, "f1_score": 0.9095607235142118, "auc_roc": 0.9974150184046524, "loss": 0.006773104500362189}, {"accuracy": 0.9858329262335125, "precision": 0.9054054054054054, "recall": 0.9617224880382775, "f1_score": 0.9327146171693734, "auc_roc": 0.9982857901505173, "loss": 0.005873051416274464}, {"accuracy": 0.9863214460185638, "precision": 0.9371980676328503, "recall": 0.9282296650717703, "f1_score": 0.9326923076923078, "auc_roc": 0.9981530267453183, "loss": 0.004516001781435072}], "learning_rates": [0.000109, 0.00020800000000000001, 0.00030700000000000004, 0.00040600000000000006, 0.000505, 0.000604, 0.000703, 0.0008020000000000001, 0.000901, 0.001, 0.0009999383779245895, 0.000999753526902683, 0.0009994454925435043, 0.000999014350849922]}