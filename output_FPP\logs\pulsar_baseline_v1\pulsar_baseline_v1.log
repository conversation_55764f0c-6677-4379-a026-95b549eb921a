2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m - Experiment 'pulsar_baseline_v1' started at 2025-07-24 02:55:44.894070
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m - Experiment Configuration:
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   experiment: ExperimentConfig(name='pulsar_baseline_v1', description='Physics-constrained lightweight Res2Net for pulsar classification (recall-optimized)', target_accuracy=0.95, random_seed=42)
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   model: ModelConfig(type='pulsar_res2net', layers=[1, 2, 2, 1], width=32, scales=3, attention_type='se', dropout_rate=0.2, num_classes=2, zero_init_residual=False, groups=1, channel_adapter={'type': 'physics_constrained', 'use_learnable': True, 'use_edge_features': True, 'edge_kernel_type': 'sobel', 'normalize_weights': True, 'init_weights': [1.0, 1.0, 1.0]})
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   data: DataConfig(dataset='htru', data_root='/yanyb/jmy/PulsarSystem/datasets/HTRU', modality='FPP', batch_size=128, num_workers=8, pin_memory=False, analyze_loader=False, augmentation={'train_config': 'aggressive', 'val_config': None}, val_split=0.2, test_split=0.1, random_state=42)
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   training: TrainingConfig(epochs=200, optimizer={'type': 'adamw', 'lr': 0.001, 'weight_decay': 0.01, 'betas': [0.9, 0.999], 'eps': 1e-08}, scheduler={'type': 'cosine_annealing', 'T_max': 200, 'eta_min': 1e-06, 'warmup_epochs': 5, 'warmup_lr': 1e-05}, loss={'type': 'focal', 'label_smoothing': 0.1, 'physics_constraint_weight': 0.01, 'focal_alpha': [0.6, 0.4], 'focal_gamma': 2.0}, regularization={'weight_decay': 0.01, 'dropout': 0.2})
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   validation: ValidationConfig(eval_interval=1, save_best=True, early_stopping_patience=10, early_stopping_min_delta=0.0001, metrics=['accuracy', 'precision', 'recall', 'f1_score', 'auc_roc'])
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   logging: LoggingConfig(log_dir='output_FPP/logs', save_dir='output_FPP/checkpoints', tensorboard=False, log_interval=10, save_interval=10)
2025-07-24 02:55:44 - pulsar_baseline_v1 - [32mINFO[0m -   hardware: HardwareConfig(device='cuda', mixed_precision=True, compile_model=False)
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m - Model Information:
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -   total_parameters: 5680102
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -   model_size_mb: 21.743328094482422
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -   model_type: PulsarRes2Net
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -   channel_adapter_weights:
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -     original_weight: 0.3333333432674408
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -     learnable_weight: 0.3333333432674408
2025-07-24 02:55:47 - pulsar_baseline_v1 - [32mINFO[0m -     edge_weight: 0.3333333432674408
2025-07-24 02:55:48 - pulsar_baseline_v1 - [32mINFO[0m - Mixed precision enabled with API type: torch_autocast_new
2025-07-24 02:55:48 - pulsar_baseline_v1 - [32mINFO[0m - Device type support: True
2025-07-24 02:55:48 - pulsar_baseline_v1 - [32mINFO[0m - Starting training: 200 epochs, 59 batches per epoch
2025-07-24 02:55:48 - pulsar_baseline_v1 - [32mINFO[0m - Starting training for 200 epochs
2025-07-24 02:56:04 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [0/7602 (0%)]	Loss: 0.070294
2025-07-24 02:56:21 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [1280/7602 (17%)]	Loss: 0.029513
2025-07-24 02:56:37 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [2560/7602 (34%)]	Loss: 0.011829
2025-07-24 02:56:57 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [3840/7602 (51%)]	Loss: 0.021517
2025-07-24 02:57:22 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [5120/7602 (68%)]	Loss: 0.020809
2025-07-24 02:57:40 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 0 [6400/7602 (85%)]	Loss: 0.048191
2025-07-24 02:57:52 - pulsar_baseline_v1 - [32mINFO[0m - Saved best checkpoint with recall: 0.6220
2025-07-24 02:57:52 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 0: Train Loss: 0.0195, Train Recall: 0.6289, Val Loss: 0.0176, Val Recall: 0.6220
2025-07-24 02:58:09 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [0/7602 (0%)]	Loss: 0.021649
2025-07-24 02:58:27 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [1280/7602 (17%)]	Loss: 0.009138
2025-07-24 02:58:44 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [2560/7602 (34%)]	Loss: 0.036125
2025-07-24 02:59:00 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [3840/7602 (51%)]	Loss: 0.008688
2025-07-24 02:59:25 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [5120/7602 (68%)]	Loss: 0.005938
2025-07-24 02:59:47 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 1 [6400/7602 (85%)]	Loss: 0.005417
2025-07-24 03:00:00 - pulsar_baseline_v1 - [32mINFO[0m - Saved best checkpoint with recall: 0.9809
2025-07-24 03:00:00 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 1: Train Loss: 0.0125, Train Recall: 0.7412, Val Loss: 0.0498, Val Recall: 0.9809
2025-07-24 03:00:16 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [0/7602 (0%)]	Loss: 0.007909
2025-07-24 03:00:32 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [1280/7602 (17%)]	Loss: 0.010796
2025-07-24 03:00:48 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [2560/7602 (34%)]	Loss: 0.009919
2025-07-24 03:01:05 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [3840/7602 (51%)]	Loss: 0.005104
2025-07-24 03:01:33 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [5120/7602 (68%)]	Loss: 0.009391
2025-07-24 03:01:50 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 2 [6400/7602 (85%)]	Loss: 0.013330
2025-07-24 03:02:03 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 2: Train Loss: 0.0108, Train Recall: 0.8091, Val Loss: 0.0153, Val Recall: 0.6746
2025-07-24 03:02:19 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [0/7602 (0%)]	Loss: 0.008957
2025-07-24 03:02:35 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [1280/7602 (17%)]	Loss: 0.021425
2025-07-24 03:02:54 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [2560/7602 (34%)]	Loss: 0.043683
2025-07-24 03:03:10 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [3840/7602 (51%)]	Loss: 0.017929
2025-07-24 03:03:37 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [5120/7602 (68%)]	Loss: 0.016994
2025-07-24 03:03:56 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 3 [6400/7602 (85%)]	Loss: 0.017737
2025-07-24 03:04:07 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 3: Train Loss: 0.0120, Train Recall: 0.7868, Val Loss: 0.0061, Val Recall: 0.8325
2025-07-24 03:04:23 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [0/7602 (0%)]	Loss: 0.006307
2025-07-24 03:04:43 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [1280/7602 (17%)]	Loss: 0.007873
2025-07-24 03:04:59 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [2560/7602 (34%)]	Loss: 0.003979
2025-07-24 03:05:16 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [3840/7602 (51%)]	Loss: 0.006997
2025-07-24 03:05:43 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [5120/7602 (68%)]	Loss: 0.002825
2025-07-24 03:05:59 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 4 [6400/7602 (85%)]	Loss: 0.010445
2025-07-24 03:06:09 - pulsar_baseline_v1 - [32mINFO[0m - Saved best checkpoint with recall: 0.9952
2025-07-24 03:06:09 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 4: Train Loss: 0.0081, Train Recall: 0.8415, Val Loss: 0.0783, Val Recall: 0.9952
2025-07-24 03:06:28 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [0/7602 (0%)]	Loss: 0.003332
2025-07-24 03:06:47 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [1280/7602 (17%)]	Loss: 0.028453
2025-07-24 03:07:03 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [2560/7602 (34%)]	Loss: 0.003514
2025-07-24 03:07:19 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [3840/7602 (51%)]	Loss: 0.021190
2025-07-24 03:07:46 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [5120/7602 (68%)]	Loss: 0.020555
2025-07-24 03:08:03 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 5 [6400/7602 (85%)]	Loss: 0.012234
2025-07-24 03:08:15 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 5: Train Loss: 0.0099, Train Recall: 0.8217, Val Loss: 0.0073, Val Recall: 0.9617
2025-07-24 03:08:32 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [0/7602 (0%)]	Loss: 0.003377
2025-07-24 03:08:50 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [1280/7602 (17%)]	Loss: 0.004206
2025-07-24 03:09:07 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [2560/7602 (34%)]	Loss: 0.001515
2025-07-24 03:09:24 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [3840/7602 (51%)]	Loss: 0.008503
2025-07-24 03:09:53 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [5120/7602 (68%)]	Loss: 0.005787
2025-07-24 03:10:10 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 6 [6400/7602 (85%)]	Loss: 0.018877
2025-07-24 03:10:21 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 6: Train Loss: 0.0076, Train Recall: 0.8510, Val Loss: 0.0176, Val Recall: 0.9713
2025-07-24 03:10:36 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [0/7602 (0%)]	Loss: 0.002814
2025-07-24 03:10:54 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [1280/7602 (17%)]	Loss: 0.001143
2025-07-24 03:11:11 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [2560/7602 (34%)]	Loss: 0.012973
2025-07-24 03:11:29 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [3840/7602 (51%)]	Loss: 0.011744
2025-07-24 03:11:53 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [5120/7602 (68%)]	Loss: 0.003376
2025-07-24 03:12:16 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 7 [6400/7602 (85%)]	Loss: 0.005173
2025-07-24 03:12:26 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 7: Train Loss: 0.0087, Train Recall: 0.8481, Val Loss: 0.0059, Val Recall: 0.8995
2025-07-24 03:12:42 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [0/7602 (0%)]	Loss: 0.003965
2025-07-24 03:13:01 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [1280/7602 (17%)]	Loss: 0.001578
2025-07-24 03:13:17 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [2560/7602 (34%)]	Loss: 0.009252
2025-07-24 03:13:36 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [3840/7602 (51%)]	Loss: 0.006009
2025-07-24 03:14:04 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [5120/7602 (68%)]	Loss: 0.003595
2025-07-24 03:14:22 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 8 [6400/7602 (85%)]	Loss: 0.005291
2025-07-24 03:14:34 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 8: Train Loss: 0.0078, Train Recall: 0.8475, Val Loss: 0.0096, Val Recall: 0.9617
2025-07-24 03:14:52 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [0/7602 (0%)]	Loss: 0.010234
2025-07-24 03:15:11 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [1280/7602 (17%)]	Loss: 0.007255
2025-07-24 03:15:27 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [2560/7602 (34%)]	Loss: 0.004823
2025-07-24 03:15:42 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [3840/7602 (51%)]	Loss: 0.005706
2025-07-24 03:16:14 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [5120/7602 (68%)]	Loss: 0.013605
2025-07-24 03:16:30 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 9 [6400/7602 (85%)]	Loss: 0.004895
2025-07-24 03:16:40 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 9: Train Loss: 0.0076, Train Recall: 0.8673, Val Loss: 0.0075, Val Recall: 0.7512
2025-07-24 03:16:58 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [0/7602 (0%)]	Loss: 0.027248
2025-07-24 03:17:15 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [1280/7602 (17%)]	Loss: 0.010279
2025-07-24 03:17:32 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [2560/7602 (34%)]	Loss: 0.012296
2025-07-24 03:17:49 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [3840/7602 (51%)]	Loss: 0.002891
2025-07-24 03:18:19 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [5120/7602 (68%)]	Loss: 0.004807
2025-07-24 03:18:36 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 10 [6400/7602 (85%)]	Loss: 0.006721
2025-07-24 03:18:48 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 10: Train Loss: 0.0074, Train Recall: 0.8651, Val Loss: 0.0075, Val Recall: 0.9569
2025-07-24 03:19:04 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [0/7602 (0%)]	Loss: 0.017741
2025-07-24 03:19:24 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [1280/7602 (17%)]	Loss: 0.009723
2025-07-24 03:19:40 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [2560/7602 (34%)]	Loss: 0.004071
2025-07-24 03:19:57 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [3840/7602 (51%)]	Loss: 0.009226
2025-07-24 03:20:24 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [5120/7602 (68%)]	Loss: 0.008236
2025-07-24 03:20:43 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 11 [6400/7602 (85%)]	Loss: 0.004522
2025-07-24 03:20:54 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 11: Train Loss: 0.0065, Train Recall: 0.8774, Val Loss: 0.0066, Val Recall: 0.9569
2025-07-24 03:21:09 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [0/7602 (0%)]	Loss: 0.003802
2025-07-24 03:21:29 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [1280/7602 (17%)]	Loss: 0.010554
2025-07-24 03:21:43 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [2560/7602 (34%)]	Loss: 0.003315
2025-07-24 03:22:04 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [3840/7602 (51%)]	Loss: 0.010173
2025-07-24 03:22:30 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [5120/7602 (68%)]	Loss: 0.001969
2025-07-24 03:22:45 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 12 [6400/7602 (85%)]	Loss: 0.005790
2025-07-24 03:22:59 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 12: Train Loss: 0.0069, Train Recall: 0.8844, Val Loss: 0.0068, Val Recall: 0.8421
2025-07-24 03:23:15 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [0/7602 (0%)]	Loss: 0.012577
2025-07-24 03:23:32 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [1280/7602 (17%)]	Loss: 0.011278
2025-07-24 03:23:50 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [2560/7602 (34%)]	Loss: 0.003358
2025-07-24 03:24:07 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [3840/7602 (51%)]	Loss: 0.001274
2025-07-24 03:24:34 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [5120/7602 (68%)]	Loss: 0.005167
2025-07-24 03:24:52 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 13 [6400/7602 (85%)]	Loss: 0.003732
2025-07-24 03:25:03 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 13: Train Loss: 0.0070, Train Recall: 0.8697, Val Loss: 0.0059, Val Recall: 0.9617
2025-07-24 03:25:21 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [0/7602 (0%)]	Loss: 0.008934
2025-07-24 03:25:39 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [1280/7602 (17%)]	Loss: 0.004100
2025-07-24 03:25:56 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [2560/7602 (34%)]	Loss: 0.006389
2025-07-24 03:26:12 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [3840/7602 (51%)]	Loss: 0.003492
2025-07-24 03:26:43 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [5120/7602 (68%)]	Loss: 0.009217
2025-07-24 03:26:58 - pulsar_baseline_v1 - [32mINFO[0m - Train Epoch: 14 [6400/7602 (85%)]	Loss: 0.008073
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Epoch 14: Train Loss: 0.0064, Train Recall: 0.8852, Val Loss: 0.0045, Val Recall: 0.9282
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Early stopping triggered after 14 epochs
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Training completed. Best recall: 0.9952 at epoch 4
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Training completed in 1859.04s
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Best model metrics:
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m -   best_recall: 0.9952153110047847
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m -   best_epoch: 4
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Experiment completed at 2025-07-24 03:27:09.241772 (duration: 0:31:24.347702)
2025-07-24 03:27:09 - pulsar_baseline_v1 - [32mINFO[0m - Loaded best checkpoint from epoch 4
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Confusion matrix saved to: output_FPP/logs/pulsar_baseline_v1/visualizations/confusion_matrix_epoch_final_test.png
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Misclassified analysis saved to: output_FPP/logs/pulsar_baseline_v1/analysis/misclassified_analysis.txt
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Metrics saved to: output_FPP/logs/pulsar_baseline_v1/metrics/test_results.json
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Test Results:
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   accuracy: 0.7811
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   precision: 0.3176
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   recall: 0.9952
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   f1_score: 0.4815
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   auc_roc: 0.9952
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m -   Misclassified samples: 448/2047
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Training history saved to: output_FPP/logs/pulsar_baseline_v1/metrics/training_history.json
2025-07-24 03:27:11 - pulsar_baseline_v1 - [32mINFO[0m - Metrics saved to: output_FPP/logs/pulsar_baseline_v1/metrics/experiment_summary.json
