"""
数据加载器模块
提供高效的数据加载和批处理功能

特性：
- 支持多进程数据加载
- 内存固定优化
- 自动批大小调整
- 数据统计和监控
"""

from typing import Optional, Tuple, Dict, Any, Union
import torch
from torch.utils.data import DataLoader, Dataset, WeightedRandomSampler
import numpy as np
from collections import Counter

from .htru_dataset import HTRUDataset, create_htru_datasets


class BalancedBatchSampler:
    """
    平衡批采样器
    确保每个批次中各类别样本数量平衡
    """
    
    def __init__(self, dataset: Dataset, batch_size: int, drop_last: bool = True):
        self.dataset = dataset
        self.batch_size = batch_size
        self.drop_last = drop_last
        
        # 统计各类别样本索引
        self.class_indices = self._get_class_indices()
        self.num_classes = len(self.class_indices)
        
        # 计算每个类别在批次中的样本数
        self.samples_per_class = batch_size // self.num_classes
        
    def _get_class_indices(self) -> Dict[int, list]:
        """获取各类别的样本索引"""
        class_indices = {}
        
        for idx in range(len(self.dataset)):
            _, label = self.dataset[idx]
            if label not in class_indices:
                class_indices[label] = []
            class_indices[label].append(idx)
        
        return class_indices
    
    def __iter__(self):
        # 为每个类别创建随机排列
        class_iterators = {}
        for class_id, indices in self.class_indices.items():
            shuffled_indices = torch.randperm(len(indices)).tolist()
            class_iterators[class_id] = iter([indices[i] for i in shuffled_indices])
        
        # 生成平衡批次
        while True:
            batch = []
            
            for class_id in self.class_indices.keys():
                for _ in range(self.samples_per_class):
                    try:
                        idx = next(class_iterators[class_id])
                        batch.append(idx)
                    except StopIteration:
                        # 重新洗牌
                        indices = self.class_indices[class_id]
                        shuffled_indices = torch.randperm(len(indices)).tolist()
                        class_iterators[class_id] = iter([indices[i] for i in shuffled_indices])
                        idx = next(class_iterators[class_id])
                        batch.append(idx)
            
            if len(batch) == self.batch_size:
                yield batch
            elif not self.drop_last and len(batch) > 0:
                yield batch
            else:
                break
    
    def __len__(self):
        min_class_size = min(len(indices) for indices in self.class_indices.values())
        return (min_class_size * self.num_classes) // self.batch_size


def create_weighted_sampler(dataset: Dataset) -> WeightedRandomSampler:
    """
    创建加权随机采样器，用于处理类别不平衡
    
    Args:
        dataset: 数据集
        
    Returns:
        加权随机采样器
    """
    # 统计各类别样本数量
    labels = []
    for idx in range(len(dataset)):
        _, label = dataset[idx]
        labels.append(label)
    
    class_counts = Counter(labels)
    total_samples = len(labels)
    
    # 计算各类别权重（逆频率）
    class_weights = {}
    for class_id, count in class_counts.items():
        class_weights[class_id] = total_samples / (len(class_counts) * count)
    
    # 为每个样本分配权重
    sample_weights = [class_weights[label] for label in labels]
    
    return WeightedRandomSampler(
        weights=sample_weights,
        num_samples=len(sample_weights),
        replacement=True
    )


def get_optimal_batch_size(
    model: torch.nn.Module,
    sample_input: torch.Tensor,
    device: torch.device,
    max_memory_gb: float = 8.0
) -> int:
    """
    自动确定最优批大小
    
    Args:
        model: 模型
        sample_input: 样本输入
        device: 设备
        max_memory_gb: 最大内存限制（GB）
        
    Returns:
        推荐的批大小
    """
    model.eval()
    model.to(device)
    
    # 测试不同批大小的内存使用
    batch_sizes = [1, 2, 4, 8, 16, 32, 64, 128]
    optimal_batch_size = 1
    
    for batch_size in batch_sizes:
        try:
            # 创建测试批次
            test_input = sample_input.repeat(batch_size, 1, 1, 1).to(device)
            
            # 清空缓存
            if device.type == 'cuda':
                torch.cuda.empty_cache()
                torch.cuda.reset_peak_memory_stats()
            
            # 前向传播
            with torch.no_grad():
                _ = model(test_input)
            
            # 检查内存使用
            if device.type == 'cuda':
                memory_used = torch.cuda.max_memory_allocated() / (1024**3)  # GB
                if memory_used < max_memory_gb:
                    optimal_batch_size = batch_size
                else:
                    break
            else:
                optimal_batch_size = batch_size
                
        except RuntimeError as e:
            if "out of memory" in str(e):
                break
            else:
                raise e
    
    return optimal_batch_size


def worker_init_fn(worker_id):
    """数据加载器工作进程初始化函数"""
    import random
    import numpy as np
    import torch

    # 为每个worker设置不同的随机种子
    worker_seed = torch.initial_seed() % 2**32
    np.random.seed(worker_seed)
    random.seed(worker_seed)


def create_efficient_dataloader(
    dataset: Dataset,
    batch_size: Optional[int] = None,
    shuffle: bool = True,
    num_workers: Optional[int] = None,
    pin_memory: Optional[bool] = None,
    use_balanced_sampling: bool = False,
    use_weighted_sampling: bool = False,
    **kwargs
) -> DataLoader:
    """
    创建高效的数据加载器
    
    Args:
        dataset: 数据集
        batch_size: 批大小（None时自动确定）
        shuffle: 是否洗牌
        num_workers: 工作进程数（None时自动确定）
        pin_memory: 是否固定内存（None时自动确定）
        use_balanced_sampling: 是否使用平衡采样
        use_weighted_sampling: 是否使用加权采样
        **kwargs: 其他DataLoader参数
        
    Returns:
        数据加载器
    """
    # 自动确定参数
    if batch_size is None:
        batch_size = 32  # 默认值
    
    if num_workers is None:
        import multiprocessing
        num_workers = min(multiprocessing.cpu_count(), 8)
    
    if pin_memory is None:
        pin_memory = torch.cuda.is_available()
    
    # 创建采样器
    sampler = None
    if use_balanced_sampling and not use_weighted_sampling:
        sampler = BalancedBatchSampler(dataset, batch_size)
        shuffle = False  # 使用采样器时不能shuffle
        batch_size = 1  # 采样器已经处理批次
    elif use_weighted_sampling and not use_balanced_sampling:
        sampler = create_weighted_sampler(dataset)
        shuffle = False  # 使用采样器时不能shuffle
    
    # 添加错误处理和优化参数
    dataloader_kwargs = {
        'dataset': dataset,
        'batch_size': batch_size,
        'shuffle': shuffle,
        'sampler': sampler,
        'num_workers': num_workers,
        'pin_memory': pin_memory,
        'drop_last': kwargs.get('drop_last', True),
        'worker_init_fn': worker_init_fn if num_workers > 0 else None,
        'persistent_workers': num_workers > 0,  # 保持worker进程
        'prefetch_factor': 2 if num_workers > 0 else 2,  # 预取因子
    }

    # 添加其他kwargs，排除已处理的
    excluded_keys = {'drop_last'}
    for k, v in kwargs.items():
        if k not in excluded_keys:
            dataloader_kwargs[k] = v

    try:
        return DataLoader(**dataloader_kwargs)
    except Exception as e:
        # 如果多进程失败，回退到单进程
        if num_workers > 0:
            print(f"Warning: Multi-process data loading failed ({e}), falling back to single process")
            dataloader_kwargs['num_workers'] = 0
            dataloader_kwargs['worker_init_fn'] = None
            dataloader_kwargs['persistent_workers'] = False
            return DataLoader(**dataloader_kwargs)
        else:
            raise e


def analyze_dataloader(dataloader: DataLoader) -> Dict[str, Any]:
    """
    分析数据加载器的统计信息
    
    Args:
        dataloader: 数据加载器
        
    Returns:
        统计信息字典
    """
    # 收集一个epoch的统计信息
    batch_sizes = []
    label_counts = Counter()
    
    for batch_idx, (inputs, targets) in enumerate(dataloader):
        batch_sizes.append(inputs.size(0))
        
        # 统计标签分布
        if isinstance(targets, torch.Tensor):
            targets = targets.cpu().numpy()
        
        for target in targets:
            label_counts[int(target)] += 1
        
        # 只分析前10个批次以节省时间
        if batch_idx >= 10:
            break
    
    # 计算统计信息
    stats = {
        'num_batches_analyzed': len(batch_sizes),
        'avg_batch_size': np.mean(batch_sizes),
        'min_batch_size': np.min(batch_sizes),
        'max_batch_size': np.max(batch_sizes),
        'label_distribution': dict(label_counts),
        'total_samples_analyzed': sum(batch_sizes)
    }
    
    # 计算类别平衡度
    if len(label_counts) > 1:
        counts = list(label_counts.values())
        balance_ratio = min(counts) / max(counts)
        stats['class_balance_ratio'] = balance_ratio
    
    return stats


# 导出的公共接口
__all__ = [
    'BalancedBatchSampler',
    'create_weighted_sampler',
    'get_optimal_batch_size',
    'create_efficient_dataloader',
    'analyze_dataloader'
]
