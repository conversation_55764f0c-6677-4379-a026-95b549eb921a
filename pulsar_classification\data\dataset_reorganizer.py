#!/usr/bin/env python3
"""
HTRU脉冲星数据集重组脚本
实施65:17.5:17.5分层抽样分割，替换当前的82.3:9.1:8.6分割

主要功能：
- 加载FPP和TPP数据
- 分层抽样确保类别比例一致
- 验证集脉冲星样本从70个增加到209个（+199%）
- 测试集从人工平衡50:50改为真实分布10.3:89.7
- 详细的日志记录和数据验证

作者：Augment Agent
日期：2025-01-21
"""

import os
import sys
import numpy as np
import logging
import shutil
from pathlib import Path
from typing import Tuple, List, Dict, Any
from collections import defaultdict
from sklearn.model_selection import train_test_split
import time

# 设置日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dataset_reorganization.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


class DatasetReorganizer:
    """
    HTRU脉冲星数据集重组器
    
    实施65:17.5:17.5分层抽样分割，解决当前配置的核心问题：
    1. 验证集脉冲星样本过少（70个 -> 209个）
    2. 测试集人工平衡分布（50:50 -> 10.3:89.7真实分布）
    3. 提供更稳定的验证和更真实的测试环境
    """
    
    def __init__(self, 
                 data_root: str = "../../datasets/HTRU",
                 output_root: str = "../../datasets/HTRU_reorganized",
                 random_state: int = 42):
        """
        初始化数据集重组器
        
        Args:
            data_root: 原始数据根目录
            output_root: 重组后数据输出目录
            random_state: 随机种子，确保可重现性
        """
        self.data_root = Path(data_root)
        self.output_root = Path(output_root)
        self.random_state = random_state
        
        # 分割比例配置
        self.train_ratio = 0.65      # 65%
        self.val_ratio = 0.175       # 17.5%
        self.test_ratio = 0.175      # 17.5%
        
        # 数据类型
        self.data_types = ['FPP', 'TPP']
        
        # 统计信息
        self.stats = {
            'original': defaultdict(int),
            'reorganized': defaultdict(int)
        }
        
        logger.info(f"初始化数据集重组器")
        logger.info(f"原始数据路径: {self.data_root.absolute()}")
        logger.info(f"输出数据路径: {self.output_root.absolute()}")
        logger.info(f"分割比例: Train {self.train_ratio:.1%}, Val {self.val_ratio:.1%}, Test {self.test_ratio:.1%}")
    
    def load_dataset_info(self, data_type: str) -> Tuple[List[str], List[int]]:
        """
        加载数据集信息（文件路径和标签）
        
        Args:
            data_type: 数据类型 ('FPP' 或 'TPP')
            
        Returns:
            Tuple[文件路径列表, 标签列表]
        """
        logger.info(f"加载 {data_type} 数据集信息...")
        
        file_paths = []
        labels = []
        
        data_dir = self.data_root / data_type
        
        # 遍历train, validation, test目录
        for split_dir in ['train', 'validation', 'test']:
            split_path = data_dir / split_dir
            if not split_path.exists():
                logger.warning(f"目录不存在: {split_path}")
                continue
            
            # 加载该分割中的所有文件
            for file_path in split_path.glob("*.npy"):
                file_paths.append(str(file_path))
                
                # 从文件名提取标签
                filename = file_path.name
                if 'positive' in filename:
                    labels.append(1)  # 脉冲星
                elif 'negative' in filename:
                    labels.append(0)  # 非脉冲星
                else:
                    logger.warning(f"无法识别标签: {filename}")
                    continue
        
        logger.info(f"{data_type} 数据加载完成: {len(file_paths)} 个文件")
        logger.info(f"脉冲星样本: {sum(labels)} 个")
        logger.info(f"非脉冲星样本: {len(labels) - sum(labels)} 个")
        
        # 更新统计信息
        self.stats['original'][f'{data_type}_total'] = len(file_paths)
        self.stats['original'][f'{data_type}_pulsar'] = sum(labels)
        self.stats['original'][f'{data_type}_non_pulsar'] = len(labels) - sum(labels)
        
        return file_paths, labels
    
    def perform_stratified_split(self, file_paths: List[str], labels: List[int]) -> Dict[str, Tuple[List[str], List[int]]]:
        """
        执行分层抽样分割
        
        Args:
            file_paths: 文件路径列表
            labels: 标签列表
            
        Returns:
            Dict包含train/val/test的文件路径和标签
        """
        logger.info("执行分层抽样分割...")
        
        # 第一步：分离测试集 (17.5%)
        X_temp, X_test, y_temp, y_test = train_test_split(
            file_paths, labels,
            test_size=self.test_ratio,
            stratify=labels,
            random_state=self.random_state
        )
        
        # 第二步：从剩余数据中分离训练集和验证集
        # 计算验证集在剩余数据中的比例: 17.5% / (100% - 17.5%) = 0.212
        val_ratio_in_remaining = self.val_ratio / (1 - self.test_ratio)
        
        X_train, X_val, y_train, y_val = train_test_split(
            X_temp, y_temp,
            test_size=val_ratio_in_remaining,
            stratify=y_temp,
            random_state=self.random_state
        )
        
        # 验证分割结果
        total_samples = len(file_paths)
        train_samples = len(X_train)
        val_samples = len(X_val)
        test_samples = len(X_test)
        
        logger.info("分割结果验证:")
        logger.info(f"训练集: {train_samples} 样本 ({train_samples/total_samples:.1%})")
        logger.info(f"验证集: {val_samples} 样本 ({val_samples/total_samples:.1%})")
        logger.info(f"测试集: {test_samples} 样本 ({test_samples/total_samples:.1%})")
        
        # 验证类别比例
        for split_name, (X_split, y_split) in [
            ('训练集', (X_train, y_train)),
            ('验证集', (X_val, y_val)),
            ('测试集', (X_test, y_test))
        ]:
            pulsar_count = sum(y_split)
            total_count = len(y_split)
            pulsar_ratio = pulsar_count / total_count if total_count > 0 else 0
            
            logger.info(f"{split_name} - 脉冲星: {pulsar_count}/{total_count} ({pulsar_ratio:.1%})")
        
        return {
            'train': (X_train, y_train),
            'val': (X_val, y_val),
            'test': (X_test, y_test)
        }
    
    def save_split_data(self, data_type: str, splits: Dict[str, Tuple[List[str], List[int]]]):
        """
        保存分割后的数据到新目录结构
        
        Args:
            data_type: 数据类型 ('FPP' 或 'TPP')
            splits: 分割后的数据
        """
        logger.info(f"保存 {data_type} 分割数据...")
        
        # 创建输出目录
        output_data_dir = self.output_root / data_type
        
        for split_name, (file_paths, labels) in splits.items():
            split_dir = output_data_dir / split_name
            split_dir.mkdir(parents=True, exist_ok=True)
            
            logger.info(f"保存 {split_name} 数据到 {split_dir}")
            
            # 复制文件到新目录
            for file_path, label in zip(file_paths, labels):
                src_path = Path(file_path)
                dst_path = split_dir / src_path.name
                
                try:
                    shutil.copy2(src_path, dst_path)
                except Exception as e:
                    logger.error(f"复制文件失败: {src_path} -> {dst_path}, 错误: {e}")
                    raise
            
            # 更新统计信息
            pulsar_count = sum(labels)
            total_count = len(labels)
            
            self.stats['reorganized'][f'{data_type}_{split_name}_total'] = total_count
            self.stats['reorganized'][f'{data_type}_{split_name}_pulsar'] = pulsar_count
            self.stats['reorganized'][f'{data_type}_{split_name}_non_pulsar'] = total_count - pulsar_count
            
            logger.info(f"{split_name} 保存完成: {total_count} 个文件 ({pulsar_count} 脉冲星)")
    
    def validate_reorganization(self):
        """验证重组结果的正确性"""
        logger.info("验证重组结果...")
        
        # 验证文件数量一致性
        for data_type in self.data_types:
            original_total = self.stats['original'][f'{data_type}_total']
            reorganized_total = (
                self.stats['reorganized'][f'{data_type}_train_total'] +
                self.stats['reorganized'][f'{data_type}_val_total'] +
                self.stats['reorganized'][f'{data_type}_test_total']
            )
            
            if original_total != reorganized_total:
                raise ValueError(f"{data_type} 文件数量不匹配: 原始 {original_total} vs 重组 {reorganized_total}")
            
            logger.info(f"✅ {data_type} 文件数量验证通过: {original_total} 个文件")
        
        # 验证类别比例
        for data_type in self.data_types:
            original_pulsar = self.stats['original'][f'{data_type}_pulsar']
            reorganized_pulsar = (
                self.stats['reorganized'][f'{data_type}_train_pulsar'] +
                self.stats['reorganized'][f'{data_type}_val_pulsar'] +
                self.stats['reorganized'][f'{data_type}_test_pulsar']
            )
            
            if original_pulsar != reorganized_pulsar:
                raise ValueError(f"{data_type} 脉冲星数量不匹配: 原始 {original_pulsar} vs 重组 {reorganized_pulsar}")
            
            logger.info(f"✅ {data_type} 脉冲星数量验证通过: {original_pulsar} 个")
        
        logger.info("✅ 所有验证通过！重组结果正确。")

    def generate_statistics_report(self) -> str:
        """
        生成详细的统计报告

        Returns:
            格式化的统计报告字符串
        """
        report = []
        report.append("=" * 80)
        report.append("HTRU脉冲星数据集重组统计报告")
        report.append("=" * 80)

        # 原始数据统计
        report.append("\n📊 原始数据集统计:")
        for data_type in self.data_types:
            total = self.stats['original'][f'{data_type}_total']
            pulsar = self.stats['original'][f'{data_type}_pulsar']
            non_pulsar = self.stats['original'][f'{data_type}_non_pulsar']
            pulsar_ratio = pulsar / total if total > 0 else 0

            report.append(f"  {data_type}:")
            report.append(f"    总样本: {total:,}")
            report.append(f"    脉冲星: {pulsar:,} ({pulsar_ratio:.1%})")
            report.append(f"    非脉冲星: {non_pulsar:,} ({1-pulsar_ratio:.1%})")

        # 重组后数据统计
        report.append("\n📈 重组后数据集统计:")
        for data_type in self.data_types:
            report.append(f"  {data_type}:")

            for split in ['train', 'val', 'test']:
                total = self.stats['reorganized'][f'{data_type}_{split}_total']
                pulsar = self.stats['reorganized'][f'{data_type}_{split}_pulsar']
                non_pulsar = self.stats['reorganized'][f'{data_type}_{split}_non_pulsar']
                pulsar_ratio = pulsar / total if total > 0 else 0

                # 计算相对于总数据的比例
                original_total = self.stats['original'][f'{data_type}_total']
                split_ratio = total / original_total if original_total > 0 else 0

                report.append(f"    {split.capitalize()}集: {total:,} 样本 ({split_ratio:.1%})")
                report.append(f"      脉冲星: {pulsar:,} ({pulsar_ratio:.1%})")
                report.append(f"      非脉冲星: {non_pulsar:,} ({1-pulsar_ratio:.1%})")

        # 关键改进指标
        report.append("\n🎯 关键改进指标:")

        # 以FPP为例计算改进
        original_val_pulsar = 70  # 当前验证集脉冲星数量
        new_val_pulsar = self.stats['reorganized']['FPP_val_pulsar']
        improvement = ((new_val_pulsar - original_val_pulsar) / original_val_pulsar) * 100

        report.append(f"  验证集脉冲星样本提升: {original_val_pulsar} → {new_val_pulsar} (+{improvement:.0f}%)")
        report.append(f"  测试集分布: 从人工平衡50:50 → 真实分布10.3:89.7")
        report.append(f"  验证稳定性: 显著提升（样本数增加{improvement:.0f}%）")

        # 数据利用效率
        total_samples = self.stats['original']['FPP_total']
        train_samples = self.stats['reorganized']['FPP_train_total']
        data_utilization = train_samples / total_samples

        report.append(f"  训练数据利用率: {data_utilization:.1%}")

        report.append("\n" + "=" * 80)

        return "\n".join(report)

    def reorganize_dataset(self):
        """
        执行完整的数据集重组流程
        """
        start_time = time.time()

        try:
            logger.info("🚀 开始HTRU脉冲星数据集重组...")

            # 检查原始数据目录
            if not self.data_root.exists():
                raise FileNotFoundError(f"原始数据目录不存在: {self.data_root}")

            # 创建输出目录
            self.output_root.mkdir(parents=True, exist_ok=True)

            # 处理每种数据类型
            for data_type in self.data_types:
                logger.info(f"\n📁 处理 {data_type} 数据...")

                # 加载数据信息
                file_paths, labels = self.load_dataset_info(data_type)

                if len(file_paths) == 0:
                    logger.warning(f"未找到 {data_type} 数据文件，跳过...")
                    continue

                # 执行分层抽样分割
                splits = self.perform_stratified_split(file_paths, labels)

                # 保存分割后的数据
                self.save_split_data(data_type, splits)

                logger.info(f"✅ {data_type} 数据处理完成")

            # 验证重组结果
            self.validate_reorganization()

            # 生成统计报告
            report = self.generate_statistics_report()
            logger.info(f"\n{report}")

            # 保存报告到文件
            report_file = self.output_root / "reorganization_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            elapsed_time = time.time() - start_time
            logger.info(f"\n🎉 数据集重组完成！耗时: {elapsed_time:.2f} 秒")
            logger.info(f"📄 详细报告已保存到: {report_file}")

        except Exception as e:
            logger.error(f"❌ 数据集重组失败: {e}")
            raise

    def cleanup_output_directory(self):
        """清理输出目录（可选）"""
        if self.output_root.exists():
            logger.warning(f"清理输出目录: {self.output_root}")
            shutil.rmtree(self.output_root)


def main():
    """主函数 - 命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(
        description="HTRU脉冲星数据集重组工具 - 实施65:17.5:17.5分层抽样分割",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python dataset_reorganizer.py                    # 使用默认路径
  python dataset_reorganizer.py --cleanup          # 先清理输出目录
  python dataset_reorganizer.py --data-root /path/to/HTRU --output-root /path/to/output
        """
    )

    parser.add_argument(
        '--data-root',
        type=str,
        default="../../datasets/HTRU",
        help="原始数据根目录路径 (默认: ../../datasets/HTRU)"
    )

    parser.add_argument(
        '--output-root',
        type=str,
        default="../../datasets/HTRU_reorganized",
        help="重组后数据输出目录路径 (默认: ../../datasets/HTRU_reorganized)"
    )

    parser.add_argument(
        '--random-state',
        type=int,
        default=42,
        help="随机种子，确保可重现性 (默认: 42)"
    )

    parser.add_argument(
        '--cleanup',
        action='store_true',
        help="执行前先清理输出目录"
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help="仅显示统计信息，不实际执行重组"
    )

    args = parser.parse_args()

    # 创建重组器
    reorganizer = DatasetReorganizer(
        data_root=args.data_root,
        output_root=args.output_root,
        random_state=args.random_state
    )

    try:
        # 可选清理
        if args.cleanup:
            reorganizer.cleanup_output_directory()

        if args.dry_run:
            logger.info("🔍 干运行模式 - 仅显示统计信息")
            for data_type in reorganizer.data_types:
                file_paths, labels = reorganizer.load_dataset_info(data_type)
                if file_paths:
                    reorganizer.perform_stratified_split(file_paths, labels)
                    logger.info(f"{data_type} 分割预览完成")
        else:
            # 执行完整重组
            reorganizer.reorganize_dataset()

    except KeyboardInterrupt:
        logger.info("⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ 执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
