"""
调试模型参数数量的脚本

分析模型各个组件的参数数量，找出参数数量异常的原因
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from models.factory import create_pulsar_res2net, get_model_info


def analyze_model_parameters(model, model_name="Model"):
    """详细分析模型各组件的参数数量"""
    print(f"\n=== {model_name} 参数分析 ===")
    
    total_params = 0
    
    for name, module in model.named_modules():
        if len(list(module.children())) == 0:  # 叶子模块
            module_params = sum(p.numel() for p in module.parameters())
            if module_params > 0:
                print(f"{name:50} {module_params:>12,} 参数")
                total_params += module_params
    
    print(f"{'='*65}")
    print(f"{'总参数数量':50} {total_params:>12,}")
    print(f"{'模型大小 (MB)':50} {total_params * 4 / 1024 / 1024:>12.2f}")
    
    return total_params


def compare_configurations():
    """对比不同配置的参数数量"""
    
    # 基础配置（仅SE）
    baseline_config = {
        'layers': [2, 3, 4, 2],
        'num_classes': 2,
        'width': 64,
        'scales': 3,
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'zero_init_residual': False,
        'groups': 1,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'sobel',
            'normalize_weights': True,
            'init_weights': [1.0, 1.0, 1.0]
        },
        'enhanced_attention': {
            'enabled': False,
            'type': None
        }
    }
    
    # SE+EMA配置（factor=4）
    ema_config = baseline_config.copy()
    ema_config['enhanced_attention'] = {
        'enabled': True,
        'type': 'ema',
        'ema_config': {'factor': 4},
        'simam_config': {'e_lambda': 1e-4}
    }
    
    # SE+EMA配置（factor=8，用于对比）
    ema_config_8 = baseline_config.copy()
    ema_config_8['enhanced_attention'] = {
        'enabled': True,
        'type': 'ema',
        'ema_config': {'factor': 8},
        'simam_config': {'e_lambda': 1e-4}
    }
    
    # SE+SimAM配置
    simam_config = baseline_config.copy()
    simam_config['enhanced_attention'] = {
        'enabled': True,
        'type': 'simam',
        'ema_config': {'factor': 4},
        'simam_config': {'e_lambda': 1e-4}
    }
    
    # 创建和分析模型
    print("创建和分析不同配置的模型...")
    
    # 1. Baseline模型
    print("\n" + "="*70)
    print("1. Baseline模型（仅SE注意力）")
    model_baseline = create_pulsar_res2net(baseline_config)
    params_baseline = analyze_model_parameters(model_baseline, "Baseline (SE only)")
    
    # 2. SE+EMA模型（factor=4）
    print("\n" + "="*70)
    print("2. SE+EMA模型（factor=4）")
    model_ema4 = create_pulsar_res2net(ema_config)
    params_ema4 = analyze_model_parameters(model_ema4, "SE+EMA (factor=4)")
    
    # 3. SE+EMA模型（factor=8）
    print("\n" + "="*70)
    print("3. SE+EMA模型（factor=8）")
    model_ema8 = create_pulsar_res2net(ema_config_8)
    params_ema8 = analyze_model_parameters(model_ema8, "SE+EMA (factor=8)")
    
    # 4. SE+SimAM模型
    print("\n" + "="*70)
    print("4. SE+SimAM模型")
    model_simam = create_pulsar_res2net(simam_config)
    params_simam = analyze_model_parameters(model_simam, "SE+SimAM")
    
    # 总结对比
    print("\n" + "="*70)
    print("参数数量对比总结")
    print("="*70)
    print(f"{'配置':25} {'参数数量':>15} {'相对baseline':>15} {'模型大小(MB)':>15}")
    print("-"*70)
    print(f"{'Baseline (SE only)':25} {params_baseline:>15,} {'+0':>15} {params_baseline*4/1024/1024:>15.2f}")
    print(f"{'SE+EMA (factor=4)':25} {params_ema4:>15,} {f'+{params_ema4-params_baseline:,}':>15} {params_ema4*4/1024/1024:>15.2f}")
    print(f"{'SE+EMA (factor=8)':25} {params_ema8:>15,} {f'+{params_ema8-params_baseline:,}':>15} {params_ema8*4/1024/1024:>15.2f}")
    print(f"{'SE+SimAM':25} {params_simam:>15,} {f'+{params_simam-params_baseline:,}':>15} {params_simam*4/1024/1024:>15.2f}")
    
    return {
        'baseline': params_baseline,
        'ema_factor4': params_ema4,
        'ema_factor8': params_ema8,
        'simam': params_simam
    }


def analyze_ema_module_specifically():
    """专门分析EMA模块的参数构成"""
    print("\n" + "="*70)
    print("EMA模块参数详细分析")
    print("="*70)
    
    from models.attention_modules.ema import EMAModule
    
    # 测试不同通道数和factor的EMA模块
    test_configs = [
        (64, 4),   # 64通道，factor=4
        (64, 8),   # 64通道，factor=8
        (128, 4),  # 128通道，factor=4
        (128, 8),  # 128通道，factor=8
        (256, 4),  # 256通道，factor=4
        (256, 8),  # 256通道，factor=8
    ]
    
    for channels, factor in test_configs:
        ema = EMAModule(channels, factor)
        ema_params = sum(p.numel() for p in ema.parameters())
        print(f"EMA({channels:3d}通道, factor={factor}): {ema_params:>8,} 参数")
        
        # 详细分析EMA内部组件
        print(f"  - GroupNorm: {sum(p.numel() for p in ema.gn.parameters()):>8,}")
        print(f"  - Conv1x1:   {sum(p.numel() for p in ema.conv1x1.parameters()):>8,}")
        print(f"  - Conv3x3:   {sum(p.numel() for p in ema.conv3x3.parameters()):>8,}")
        print()


def main():
    """主函数"""
    print("脉冲星分类模型参数数量调试分析")
    print("="*70)
    
    # 1. 对比不同配置
    param_counts = compare_configurations()
    
    # 2. 专门分析EMA模块
    analyze_ema_module_specifically()
    
    # 3. 分析问题
    print("\n" + "="*70)
    print("问题分析")
    print("="*70)
    
    expected_ema4 = 46257230  # 我们预期的SE+EMA(factor=4)参数数量
    actual_ema4 = param_counts['ema_factor4']
    
    print(f"预期SE+EMA(factor=4)参数数量: {expected_ema4:,}")
    print(f"实际SE+EMA(factor=4)参数数量: {actual_ema4:,}")
    print(f"差异: {actual_ema4 - expected_ema4:,}")
    
    if actual_ema4 != expected_ema4:
        print("\n可能的原因：")
        print("1. 预期参数数量计算错误")
        print("2. EMA模块实现与预期不符")
        print("3. 模型其他组件的参数数量变化")
        print("4. 配置参数（width, scales等）与预期不符")
    
    # 4. 更新预期参数数量映射
    print("\n" + "="*70)
    print("建议的参数数量映射更新")
    print("="*70)
    print("EXPECTED_PARAM_COUNTS = {")
    print(f"    'baseline': {param_counts['baseline']},")
    print(f"    'se_only': {param_counts['baseline']},")
    print(f"    'se_ema_factor4': {param_counts['ema_factor4']},")
    print(f"    'se_ema_factor8': {param_counts['ema_factor8']},")
    print(f"    'se_simam': {param_counts['simam']},")
    print(f"    'fpp_se_ema': {param_counts['ema_factor4']},")
    print(f"    'tpp_se_simam': {param_counts['simam']},")
    print("}")


if __name__ == "__main__":
    main()
