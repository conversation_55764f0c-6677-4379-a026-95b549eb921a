{"timestamp": "2025-07-23T13:14:49.894600", "config": {"experiment_name": "PulsarSystem", "save_dir": "output_FPP/logs", "metadata_path": "output_FPP/logs/PulsarSystem/metadata.json"}, "environment": {"platform": {"system": "Linux", "release": "5.15.0-60-generic", "version": "#66-Ubuntu SMP Fri Jan 20 14:29:49 UTC 2023", "machine": "x86_64", "processor": "x86_64", "python_version": "3.9.21"}, "python": {"version": "3.9.21 (main, Dec 11 2024, 16:24:11) \n[GCC 11.2.0]", "executable": "/yanyb/jmy/miniconda3/envs/wgan_env/bin/python", "path": ["/yanyb/jmy/PulsarSystem/pulsar_classification", "/yanyb/jmy/PulsarSystem/pulsar_classification/experiments", "/yanyb/jmy/miniconda3/envs/wgan_env/lib/python39.zip"]}, "pytorch": {"version": "2.1.2", "cuda_available": true, "cuda_version": "11.8", "cudnn_version": 8700, "device_count": 1}, "gpu": [{"name": "NVIDIA A100-SXM4-40GB", "memory_total": 42298834944, "memory_free": 42298834944, "compute_capability": "8.0"}], "numpy_version": "1.26.4", "sklearn_version": "1.6.1", "pillow_version": "11.1.0"}, "dependencies": {"torch": true, "torchvision": true, "numpy": true, "pillow": true, "sklearn": true, "yaml": true}, "random_seed": 42}