# 脉冲星分类Baseline模型

基于物理约束的Res2Net架构实现，专门用于脉冲星分类任务。本项目集成了混合通道适配器、SE注意力机制和物理约束数据增强，目标实现94-97%的准确率作为baseline性能。

## 🎯 项目特点

### 理论创新
- **混合通道适配器**：实现理论公式 `A(X) = α₁X + α₂Conv1×1(X) + α₃Edge(X)`
- **物理约束设计**：严格遵循脉冲星天体物理学原理
- **多尺度特征提取**：Res2Net架构适配脉冲星信号的多时间尺度特性

### 技术特性
- 🔬 **学术标准**：符合论文发表要求的严格实现
- 🧪 **可重现性**：完整的随机种子控制和环境记录
- ⚡ **高性能**：支持混合精度训练和GPU加速
- 📊 **全面监控**：TensorBoard集成和详细指标记录
- 🔧 **模块化设计**：独立可测试的组件架构

## 📁 项目结构

```
pytorch-res2net-master/
├── pulsar_classification/           # 主要模块包
│   ├── models/                     # 模型定义
│   │   ├── channel_adapters.py     # 混合通道适配器 [核心创新]
│   │   ├── pulsar_res2net.py      # 专门化Res2Net模型
│   │   ├── attention.py           # SE注意力机制
│   │   └── factory.py             # 模型工厂函数
│   ├── data/                      # 数据处理
│   │   ├── htru_dataset.py        # HTRU数据集类
│   │   ├── transforms.py          # 物理约束数据增强
│   │   └── data_loader.py         # 数据加载器
│   ├── training/                  # 训练系统
│   │   ├── trainer.py             # 训练器类
│   │   ├── losses.py              # 多目标损失函数
│   │   └── metrics.py             # 脉冲星特定评估指标
│   ├── configs/                   # 配置管理
│   │   └── base_config.py         # 基础配置类
│   └── utils/                     # 工具函数
│       ├── logger.py              # 日志记录系统
│       └── reproducibility.py     # 可重现性工具
├── experiments/                   # 实验脚本
│   └── train_baseline.py          # 基线模型训练
├── tests/                         # 测试文件
│   └── test_basic_functionality.py # 基础功能测试
├── configs/                       # YAML配置文件
│   └── baseline.yaml              # 基线配置
├── requirements.txt               # Python依赖
└── README.md                      # 项目文档
```

## 🚀 快速开始

### 1. 环境设置

```bash
# 克隆项目
git clone <repository-url>
cd pytorch-res2net-master

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据准备

将HTRU数据集组织为以下结构：

```
datasets/HTRU/
├── FPP/
│   ├── train/
│   │   ├── pulsar/
│   │   └── non_pulsar/
│   ├── val/
│   └── test/
└── TPP/
    ├── train/
    ├── val/
    └── test/
```

### 3. 基础功能测试

```bash
# 运行基础功能测试
python tests/test_basic_functionality.py
```

### 4. 训练baseline模型

```bash
# 使用默认配置训练
python experiments/train_baseline.py --config configs/baseline.yaml

# 调试模式（快速验证）
python experiments/train_baseline.py --config configs/baseline.yaml --debug

# 自定义参数
python experiments/train_baseline.py \
    --config configs/baseline.yaml \
    --batch-size 64 \
    --lr 0.002 \
    --epochs 100
```

## 📊 模型架构

### 核心组件

#### 1. 混合通道适配器
```python
# 理论公式：A(X) = α₁X + α₂Conv1×1(X) + α₃Edge(X)
adapter = PhysicsConstrainedChannelAdapter(
    use_learnable=True,      # 可学习1×1卷积
    use_edge_features=True,  # 物理边缘特征
    edge_kernel_type='sobel' # Sobel边缘检测
)
```

#### 2. 专门化Res2Net
```python
model = PulsarRes2Net(
    layers=[3, 4, 6],        # 网络深度
    width=64,                # 基础宽度
    scales=4,                # 多尺度数量
    attention_type='se',     # SE注意力
    num_classes=2            # 二分类
)
```

#### 3. 物理约束数据增强
```python
# 物理上合理的变换
transforms = create_physics_constrained_transforms(
    mode='train',
    config='moderate'  # conservative, moderate, aggressive
)
```

## 🔧 配置说明

### 基础配置 (configs/baseline.yaml)

```yaml
# 模型配置
model:
  layers: [3, 4, 6]
  width: 64
  scales: 4
  attention_type: "se"
  channel_adapter:
    type: "physics_constrained"
    use_learnable: true
    use_edge_features: true

# 训练配置
training:
  epochs: 200
  optimizer:
    type: "adamw"
    lr: 0.001
    weight_decay: 0.01
  scheduler:
    type: "cosine_annealing"
```

## 📈 性能指标

### 目标性能
- **准确率**：94-97%
- **精确率**：>95%
- **召回率**：>95%
- **F1分数**：>95%
- **假阳性率**：<5%

### 评估指标
- 标准分类指标：准确率、精确率、召回率、F1分数
- 脉冲星特定指标：检测效率、污染率、效率-纯度乘积
- 混淆矩阵和分类报告

## 🧪 实验功能

### 训练监控
- TensorBoard可视化
- 实时指标记录
- 学习率调度监控
- 模型权重分析

### 检查点管理
- 自动保存最佳模型
- 训练恢复功能
- 早停机制
- 模型版本控制

### 可重现性
- 随机种子控制
- 环境信息记录
- 配置版本管理
- 依赖版本锁定

## 🔬 物理约束原则

### 合理的数据增强
✅ **相位旋转**：脉冲星信号的相位可以任意旋转  
✅ **时间翻转**：等价于观测时间反演  
✅ **观测噪声**：模拟不同观测条件  
✅ **信号缩放**：模拟不同观测强度  
✅ **相位偏移**：模拟观测时间偏差  

### 禁止的变换
❌ **非线性变形**：破坏色散关系  
❌ **随机裁剪**：破坏完整脉冲轮廓  
❌ **颜色变换**：不适用于单通道数据  

## 📚 API文档

### 模型创建
```python
from pulsar_classification.models.factory import create_pulsar_res2net

config = {
    'layers': [3, 4, 6],
    'width': 64,
    'scales': 4,
    'attention_type': 'se'
}
model = create_pulsar_res2net(config)
```

### 数据加载
```python
from pulsar_classification.data.htru_dataset import create_htru_datasets

datasets = create_htru_datasets(
    data_root='path/to/htru',
    modality='FPP',
    transform_config='moderate'
)
```

### 训练器使用
```python
from pulsar_classification.training.trainer import PulsarTrainer

trainer = PulsarTrainer(
    model=model,
    train_loader=train_loader,
    val_loader=val_loader,
    config=config,
    device=device
)
history = trainer.train(epochs=200)
```

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢Res2Net原始论文的作者
- 感谢HTRU数据集的提供者
- 感谢脉冲星天体物理学社区的理论支持

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件至：[<EMAIL>]
- 项目主页：[project-homepage]
