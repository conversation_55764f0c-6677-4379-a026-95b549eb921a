"""
模型架构验证工具

提供自动验证模型架构与配置一致性的功能，防止配置传递问题。
"""

from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn

# 尝试相对导入，如果失败则使用绝对导入
try:
    from ..models.factory import create_pulsar_res2net, get_model_info
except ImportError:
    from models.factory import create_pulsar_res2net, get_model_info


# 预期的模型参数数量映射（基于实际测试结果更新）
EXPECTED_PARAM_COUNTS = {
    # 基础配置（仅SE注意力）
    'baseline': 39059531,
    'se_only': 39059531,

    # 串联注意力架构 - 注意：EMA factor越小参数越多
    'se_ema_factor4': 67831883,    # SE + EMA (factor=4) - 更多参数，更高内存消耗
    'se_ema_factor8': 46257227,    # SE + EMA (factor=8) - 较少参数，较低内存消耗
    'se_simam': 39059531,          # SE + SimAM (SimAM无额外参数)

    # 不同模态的预期参数数量（使用factor=4的EMA）
    'fpp_se_ema': 67831883,        # FPP模态使用EMA factor=4
    'tpp_se_simam': 39059531,      # TPP模态使用SimAM

    # 向后兼容的映射
    'se_ema': 67831883,            # 默认使用factor=4
}


def get_expected_param_count(config: Dict[str, Any]) -> int:
    """
    根据配置获取预期的模型参数数量

    Args:
        config: 模型配置字典

    Returns:
        预期的参数数量
    """
    # 检查是否启用了增强注意力
    enhanced_attention = config.get('enhanced_attention', {})

    if not enhanced_attention.get('enabled', False):
        return EXPECTED_PARAM_COUNTS['baseline']

    attention_type = enhanced_attention.get('type')

    if attention_type == 'ema':
        # 根据EMA factor确定参数数量
        ema_config = enhanced_attention.get('ema_config', {})
        factor = ema_config.get('factor', 4)  # 默认factor=4

        if factor == 4:
            return EXPECTED_PARAM_COUNTS['se_ema_factor4']
        elif factor == 8:
            return EXPECTED_PARAM_COUNTS['se_ema_factor8']
        else:
            # 对于其他factor值，使用factor=4的参数数量作为近似
            return EXPECTED_PARAM_COUNTS['se_ema_factor4']
    elif attention_type == 'simam':
        return EXPECTED_PARAM_COUNTS['se_simam']
    else:
        # 默认返回baseline参数数量
        return EXPECTED_PARAM_COUNTS['baseline']


def validate_model_architecture(config: Dict[str, Any], model: nn.Module) -> Tuple[bool, str]:
    """
    验证模型架构是否与配置一致

    Args:
        config: 模型配置字典
        model: 模型实例

    Returns:
        (is_valid, message): 验证结果和消息
    """
    try:
        # 获取实际参数数量
        actual_params = sum(p.numel() for p in model.parameters())

        # 获取预期参数数量
        expected_params = get_expected_param_count(config)

        # 验证参数数量
        if actual_params == expected_params:
            return True, f"✅ 模型架构验证通过: {actual_params:,} 参数"
        else:
            enhanced_attention = config.get('enhanced_attention', {})
            enabled = enhanced_attention.get('enabled', False)
            attention_type = enhanced_attention.get('type', 'none')

            message = (
                f"❌ 模型架构验证失败:\n"
                f"  - 预期参数数量: {expected_params:,}\n"
                f"  - 实际参数数量: {actual_params:,}\n"
                f"  - 差异: {actual_params - expected_params:,}\n"
                f"  - 增强注意力配置: enabled={enabled}, type={attention_type}\n"
                f"  - 可能原因: enhanced_attention配置未正确传递到模型"
            )
            return False, message

    except Exception as e:
        return False, f"❌ 模型架构验证出错: {str(e)}"


def validate_enhanced_attention_config(config: Dict[str, Any]) -> Tuple[bool, str]:
    """
    验证增强注意力配置的有效性

    Args:
        config: 模型配置字典

    Returns:
        (is_valid, message): 验证结果和消息
    """
    enhanced_attention = config.get('enhanced_attention', {})

    if not enhanced_attention:
        return True, "✅ 未配置增强注意力，使用默认配置"

    enabled = enhanced_attention.get('enabled', False)

    if not enabled:
        return True, "✅ 增强注意力已禁用"

    attention_type = enhanced_attention.get('type')

    if not attention_type:
        return False, "❌ 增强注意力已启用但未指定类型"

    if attention_type not in ['ema', 'simam']:
        return False, f"❌ 不支持的增强注意力类型: {attention_type}"

    # 验证EMA配置
    if attention_type == 'ema':
        ema_config = enhanced_attention.get('ema_config', {})
        factor = ema_config.get('factor', 8)
        if not isinstance(factor, int) or factor <= 0:
            return False, f"❌ EMA factor必须是正整数: {factor}"

    # 验证SimAM配置
    if attention_type == 'simam':
        simam_config = enhanced_attention.get('simam_config', {})
        e_lambda = simam_config.get('e_lambda', 1e-4)
        if not isinstance(e_lambda, (int, float)) or e_lambda <= 0:
            return False, f"❌ SimAM e_lambda必须是正数: {e_lambda}"

    return True, f"✅ 增强注意力配置有效: type={attention_type}"


def auto_validate_model_creation(config: Dict[str, Any]) -> Tuple[nn.Module, Dict[str, Any], bool]:
    """
    自动验证模型创建过程

    Args:
        config: 模型配置字典

    Returns:
        (model, model_info, is_valid): 模型实例、模型信息和验证结果
    """
    print("🔍 开始模型架构自动验证...")

    # 1. 验证增强注意力配置
    config_valid, config_msg = validate_enhanced_attention_config(config)
    print(f"   {config_msg}")

    if not config_valid:
        raise ValueError(f"配置验证失败: {config_msg}")

    # 2. 创建模型
    try:
        model = create_pulsar_res2net(config)
        model_info = get_model_info(model)
        print(f"   ✅ 模型创建成功: {model_info['total_parameters']:,} 参数")
    except Exception as e:
        raise RuntimeError(f"模型创建失败: {str(e)}")

    # 3. 验证模型架构
    arch_valid, arch_msg = validate_model_architecture(config, model)
    print(f"   {arch_msg}")

    if not arch_valid:
        print("   ⚠️  警告: 模型架构验证失败，可能存在配置传递问题")

    # 4. 输出详细信息
    enhanced_attention = config.get('enhanced_attention', {})
    if enhanced_attention.get('enabled', False):
        attention_type = enhanced_attention.get('type', 'unknown')
        print(f"   📊 串联注意力架构: SE + {attention_type.upper()}")
    else:
        print(f"   📊 基础注意力架构: SE only")

    print(f"   📊 模型大小: {model_info['model_size_mb']:.2f} MB")

    return model, model_info, arch_valid


def create_validation_report(config: Dict[str, Any], model: nn.Module) -> Dict[str, Any]:
    """
    创建详细的验证报告

    Args:
        config: 模型配置字典
        model: 模型实例

    Returns:
        验证报告字典
    """
    report = {
        'timestamp': torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU',
        'config_validation': {},
        'architecture_validation': {},
        'model_info': {}
    }

    # 配置验证
    config_valid, config_msg = validate_enhanced_attention_config(config)
    report['config_validation'] = {
        'valid': config_valid,
        'message': config_msg,
        'enhanced_attention': config.get('enhanced_attention', {})
    }

    # 架构验证
    arch_valid, arch_msg = validate_model_architecture(config, model)
    report['architecture_validation'] = {
        'valid': arch_valid,
        'message': arch_msg,
        'expected_params': get_expected_param_count(config),
        'actual_params': sum(p.numel() for p in model.parameters())
    }

    # 模型信息
    model_info = get_model_info(model)
    report['model_info'] = model_info

    # 总体验证结果
    report['overall_valid'] = config_valid and arch_valid

    return report


def print_validation_summary(report: Dict[str, Any]):
    """
    打印验证摘要

    Args:
        report: 验证报告字典
    """
    print("\n" + "="*60)
    print("🔍 模型架构验证摘要")
    print("="*60)

    # 配置验证结果
    config_result = "✅ 通过" if report['config_validation']['valid'] else "❌ 失败"
    print(f"📋 配置验证: {config_result}")
    print(f"   {report['config_validation']['message']}")

    # 架构验证结果
    arch_result = "✅ 通过" if report['architecture_validation']['valid'] else "❌ 失败"
    print(f"🏗️  架构验证: {arch_result}")
    print(f"   预期参数: {report['architecture_validation']['expected_params']:,}")
    print(f"   实际参数: {report['architecture_validation']['actual_params']:,}")

    # 总体结果
    overall_result = "✅ 全部通过" if report['overall_valid'] else "❌ 存在问题"
    print(f"🎯 总体验证: {overall_result}")

    if not report['overall_valid']:
        print("\n⚠️  建议检查:")
        print("   1. enhanced_attention配置是否正确传递到模型工厂函数")
        print("   2. 模型工厂函数是否正确处理enhanced_attention参数")
        print("   3. 注意力模块是否正确集成到模型中")

    print("="*60)
