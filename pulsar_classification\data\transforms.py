"""
物理约束的数据增强模块 (v2.0)
严格遵循脉冲星天体物理学原理的数据变换

版本更新 (v2.0 新功能)：
✨ 新增三个基于天体物理学的智能数据增强变换：
1. 色散延迟变换 (DispersionDelayTransform) - 基于星际介质色散效应
2. 散射效应变换 (ScatteringTransform) - 基于星际介质散射导致的指数尾部
3. 多普勒效应变换 (DopplerShiftTransform) - 基于轨道运动的频率偏移

物理约束原则：
1. 相位旋转：脉冲星信号的相位可以任意旋转 - 物理合理
2. 时间翻转：在某些情况下等价于观测时间反演 - 谨慎使用
3. 观测噪声：模拟不同观测条件下的噪声 - 物理合理
4. 信号缩放：模拟不同观测条件下的信号强度 - 物理合理
5. 相位偏移：模拟观测时间偏差 - 物理合理
6. 🆕 色散延迟：模拟星际介质中的频率相关传播延迟 - 物理合理
7. 🆕 散射效应：模拟星际介质散射导致的信号展宽 - 物理合理
8. 🆕 多普勒效应：模拟轨道运动导致的频率偏移 - 物理合理

禁止的变换：
- 非线性变形：破坏色散关系
- 随机裁剪：破坏完整脉冲轮廓
- 颜色变换：不适用于单通道数据

使用示例：
    # 基本使用 - 使用预定义配置
    from pulsar_classification.data.transforms import create_physics_constrained_transforms

    # 创建训练时的变换（包含所有新的物理效应）
    train_transforms = create_physics_constrained_transforms(
        mode='train',
        config=None  # 使用默认的 'moderate' 配置
    )

    # 应用变换
    augmented_data = train_transforms(original_data)

    # 高级使用 - 自定义配置
    custom_config = {
        'use_dispersion_delay': True,
        'dm_variation_range': (-0.05, 0.05),
        'dispersion_probability': 0.3,
        'use_scattering': True,
        'tau_scatter_range': (0.1, 1.5),
        'scattering_probability': 0.2,
        'use_doppler_shift': True,
        'velocity_range': (-20, 20),
        'doppler_probability': 0.4,
        'to_tensor': False  # 如果输入已经是tensor
    }

    custom_transforms = create_physics_constrained_transforms(
        mode='train',
        config=custom_config
    )

    # 单独使用新的物理变换
    from pulsar_classification.data.transforms import (
        DispersionDelayTransform,
        ScatteringTransform,
        DopplerShiftTransform
    )

    # 色散延迟变换
    dispersion = DispersionDelayTransform(
        dm_variation_range=(-0.1, 0.1),
        probability=0.5
    )

    # 散射效应变换
    scattering = ScatteringTransform(
        tau_scatter_range=(0.1, 2.0),
        probability=0.3
    )

    # 多普勒效应变换
    doppler = DopplerShiftTransform(
        velocity_range=(-30, 30),
        probability=0.4
    )

    # 应用单个变换
    dispersed_data = dispersion(original_data)
    scattered_data = scattering(original_data)
    doppler_shifted_data = doppler(original_data)

最佳实践：
1. 使用 'conservative' 配置进行初步实验
2. 使用 'moderate' 配置进行标准训练
3. 使用 'aggressive' 配置进行数据稀缺场景
4. 始终验证物理合理性：validate_physics_constraints()
5. 监控性能影响：新变换增加约10-50%的计算开销
6. 根据具体脉冲星类型调整参数范围

"""

from typing import Tuple, Optional, Union, Callable, Dict, Any
import torch
import torch.nn.functional as F
from torch import Tensor
import torchvision.transforms as transforms
import numpy as np
import random
import math
import scipy.signal
from scipy import interpolate


class PhysicsConstrainedRotation:
    """
    物理约束的旋转变换
    脉冲星信号的相位可以任意旋转 - 物理上合理
    """
    
    def __init__(self, degrees: Union[float, Tuple[float, float]] = 180):
        if isinstance(degrees, (int, float)):
            self.degrees = (-degrees, degrees)
        else:
            self.degrees = degrees
    
    def __call__(self, img: Tensor) -> Tensor:
        angle = random.uniform(self.degrees[0], self.degrees[1])
        return transforms.functional.rotate(img, angle, fill=0)


class PhysicsConstrainedFlip:
    """
    物理约束的翻转变换
    水平翻转：时间反演，在某些情况下物理合理
    垂直翻转：需要根据FPP/TPP模态谨慎使用
    """
    
    def __init__(self, horizontal_prob: float = 0.5, vertical_prob: float = 0.0):
        self.horizontal_prob = horizontal_prob
        self.vertical_prob = vertical_prob
    
    def __call__(self, img: Tensor) -> Tensor:
        if random.random() < self.horizontal_prob:
            img = transforms.functional.hflip(img)
        if random.random() < self.vertical_prob:
            img = transforms.functional.vflip(img)
        return img


class ObservationalNoise:
    """
    观测噪声模拟
    模拟不同观测条件下的噪声 - 物理上合理
    """
    
    def __init__(self, noise_std: float = 0.01, snr_range: Tuple[float, float] = (0.8, 1.2)):
        self.noise_std = noise_std
        self.snr_range = snr_range
    
    def __call__(self, img: Tensor) -> Tensor:
        # 添加高斯噪声
        noise = torch.randn_like(img) * self.noise_std
        
        # 信噪比变化
        snr_factor = random.uniform(self.snr_range[0], self.snr_range[1])
        
        return img * snr_factor + noise


class PhaseShift:
    """
    相位偏移变换
    模拟观测时间偏差导致的相位偏移 - 物理上合理
    """
    
    def __init__(self, max_shift_ratio: float = 0.1):
        self.max_shift_ratio = max_shift_ratio
    
    def __call__(self, img: Tensor) -> Tensor:
        # 确保输入是3D张量 [C, H, W]
        if img.dim() == 2:  # [H, W]
            img = img.unsqueeze(0)  # [1, H, W]
        elif img.dim() == 3 and img.size(0) != 1:  # [C, H, W] with C != 1
            raise ValueError("PhaseShift only supports single-channel images")
        elif img.dim() != 3:
            raise ValueError(f"Expected 2D or 3D tensor, got {img.dim()}D")

        _, _, w = img.shape
        max_shift = int(w * self.max_shift_ratio)
        shift = random.randint(-max_shift, max_shift)

        if shift != 0:
            img = torch.roll(img, shift, dims=2)  # 水平方向循环移位

        # 始终返回3D张量 [C, H, W]
        return img


class IntensityScaling:
    """
    强度缩放变换
    模拟不同观测条件下的信号强度变化 - 物理上合理
    """
    
    def __init__(self, scale_range: Tuple[float, float] = (0.8, 1.2)):
        self.scale_range = scale_range
    
    def __call__(self, img: Tensor) -> Tensor:
        scale = random.uniform(self.scale_range[0], self.scale_range[1])
        return img * scale


class DispersionDelayTransform:
    """
    色散延迟变换 - 基于脉冲星色散量度的频率相关延迟

    物理原理：
    脉冲星信号在星际介质中传播时，不同频率的信号会有不同的传播延迟。
    低频信号比高频信号延迟更多，这是由于星际介质中自由电子的色散效应。

    数学公式：
    t₂ - t₁ = 4.15 ms × DM × [(ν₁/GHz)⁻² - (ν₂/GHz)⁻²]

    其中：
    - DM是色散量度 (pc cm⁻³)
    - ν₁, ν₂是两个不同的观测频率 (GHz)
    - t₂ - t₁是两个频率之间的时间延迟 (ms)

    实现方法：
    对图像的不同频率通道（行）应用不同的时间延迟（列方向的循环移位）

    Args:
        dm_variation_range: DM变化范围，相对于原始值的比例 (默认: (-0.1, 0.1))
        probability: 应用变换的概率 (默认: 0.5)
    """

    def __init__(self, dm_variation_range: Tuple[float, float] = (-0.1, 0.1), probability: float = 0.5):
        self.dm_variation_range = dm_variation_range
        self.probability = probability

    def __call__(self, img: Tensor) -> Tensor:
        # 50%概率应用变换
        if torch.rand(1).item() > self.probability:
            return img

        # 确保输入是正确的格式
        if img.dim() == 2:  # [H, W]
            img = img.unsqueeze(0)  # [1, H, W]
        elif img.dim() != 3:
            raise ValueError(f"Expected 2D or 3D tensor, got {img.dim()}D")

        # 生成DM变化因子
        dm_factor = 1 + torch.rand(1).item() * (
            self.dm_variation_range[1] - self.dm_variation_range[0]
        ) + self.dm_variation_range[0]

        # 获取图像尺寸
        channels, height, width = img.shape
        result = img.clone()

        # 对每个频率通道应用不同的时间延迟
        for freq_idx in range(height):
            # 计算频率相关的延迟（简化模型）
            # 假设频率从高到低排列（freq_idx=0为最高频率）
            freq_factor = (freq_idx / max(height - 1, 1)) + 0.1  # 避免除零，范围[0.1, 1.1]

            # 计算延迟样本数（低频延迟更多）
            # 最大延迟为宽度的2%，确保物理合理性
            delay_samples = int(dm_factor * freq_factor * width * 0.02)

            if delay_samples > 0:
                # 应用循环移位（正值表示向右移动，模拟延迟）
                for c in range(channels):
                    result[c, freq_idx, :] = torch.roll(
                        result[c, freq_idx, :], delay_samples, dims=0
                    )

        return result


class ScatteringTransform:
    """
    星际散射效应变换 - 模拟星际介质散射导致的指数尾部

    物理原理：
    脉冲星信号在传播过程中会受到星际介质中电子密度不均匀性的影响，
    导致信号产生散射效应。这种散射会在脉冲信号后面产生指数衰减的尾部，
    这是脉冲星观测中常见的现象。

    数学表达：
    signal_out = signal_in ⊗ exp(-t/τ_scatter)

    其中：
    - ⊗ 表示卷积操作
    - τ_scatter 是散射时间常数，决定尾部衰减的快慢
    - t 是时间轴（对应图像的列方向）

    物理意义：
    - τ_scatter 越大，散射尾部越长，信号展宽越明显
    - 散射效应在低频更明显，但这里简化为对所有频率通道同等处理
    - 散射强度与观测频率、脉冲星距离和星际介质密度相关

    实现方法：
    使用一维卷积对每个频率通道的时间序列进行处理，
    卷积核为指数衰减函数，模拟散射的物理过程。

    Args:
        tau_scatter_range: 散射时间常数范围，单位为样本数 (默认: (0.1, 2.0))
        probability: 应用变换的概率 (默认: 0.3)
    """

    def __init__(self, tau_scatter_range: Tuple[float, float] = (0.1, 2.0), probability: float = 0.3):
        self.tau_scatter_range = tau_scatter_range
        self.probability = probability

    def __call__(self, img: Tensor) -> Tensor:
        # 30%概率应用变换（散射不是所有脉冲星都明显）
        if torch.rand(1).item() > self.probability:
            return img

        # 确保输入是正确的格式
        if img.dim() == 2:  # [H, W]
            img = img.unsqueeze(0)  # [1, H, W]
        elif img.dim() != 3:
            raise ValueError(f"Expected 2D or 3D tensor, got {img.dim()}D")

        # 生成散射时间常数
        tau = torch.rand(1).item() * (
            self.tau_scatter_range[1] - self.tau_scatter_range[0]
        ) + self.tau_scatter_range[0]

        # 获取图像尺寸
        channels, height, width = img.shape

        # 创建指数衰减核
        # 核大小限制为tau的10倍或宽度的1/4，取较小值确保合理性
        kernel_size = min(int(tau * 10), width // 4, 20)  # 最大核大小为20
        if kernel_size < 1:
            kernel_size = 1

        t = torch.arange(kernel_size, dtype=torch.float32, device=img.device)
        kernel = torch.exp(-t / tau)
        kernel = kernel / kernel.sum()  # 归一化确保能量守恒

        # 重塑核为conv1d所需的形状 [out_channels, in_channels, kernel_length]
        kernel = kernel.unsqueeze(0).unsqueeze(0)  # [1, 1, kernel_size]

        result = img.clone()

        # 对每个频率通道分别应用卷积
        for c in range(channels):
            for h in range(height):
                # 提取单个频率通道的时间序列 [1, 1, width]
                signal = img[c, h, :].unsqueeze(0).unsqueeze(0)

                # 应用一维卷积，使用'same'模式保持长度不变
                padding = kernel_size // 2
                convolved = F.conv1d(signal, kernel, padding=padding)

                # 确保输出长度与输入相同
                if convolved.shape[-1] != width:
                    # 如果长度不匹配，进行裁剪或填充
                    if convolved.shape[-1] > width:
                        convolved = convolved[:, :, :width]
                    else:
                        pad_size = width - convolved.shape[-1]
                        convolved = F.pad(convolved, (0, pad_size))

                # 将结果写回
                result[c, h, :] = convolved.squeeze()

        return result


class DopplerShiftTransform:
    """
    多普勒效应变换 - 模拟轨道运动导致的频率偏移

    物理原理：
    脉冲星的轨道运动（如双星系统中的轨道运动）和地球的运动会导致
    观测到的脉冲星信号频率发生偏移，这就是多普勒效应。

    数学公式：
    Δf/f = v/c

    其中：
    - Δf 是频率偏移
    - f 是原始频率
    - v 是径向速度（正值表示远离，负值表示接近）
    - c 是光速 (299,792.458 km/s)

    物理背景：
    - 脉冲星双星系统：轨道运动导致周期性的频率偏移
    - 地球运动：地球绕太阳公转和自转导致的多普勒偏移
    - 银河系运动：太阳系在银河系中的运动

    观测效应：
    - 频率偏移会导致脉冲到达时间的变化
    - 在频率-时间图中表现为频率通道的整体偏移
    - 偏移量与观测频率成正比

    实现方法：
    通过重新采样频率轴来模拟频率偏移效应，
    使用线性插值确保信号的连续性和物理合理性。

    Args:
        velocity_range: 径向速度范围，单位km/s (默认: (-30, 30))
        probability: 应用变换的概率 (默认: 0.4)
    """

    def __init__(self, velocity_range: Tuple[float, float] = (-30, 30), probability: float = 0.4):
        self.velocity_range = velocity_range
        self.probability = probability

    def __call__(self, img: Tensor) -> Tensor:
        # 40%概率应用变换
        if torch.rand(1).item() > self.probability:
            return img

        # 确保输入是正确的格式
        if img.dim() == 2:  # [H, W]
            img = img.unsqueeze(0)  # [1, H, W]
        elif img.dim() != 3:
            raise ValueError(f"Expected 2D or 3D tensor, got {img.dim()}D")

        # 生成径向速度
        velocity = torch.rand(1).item() * (
            self.velocity_range[1] - self.velocity_range[0]
        ) + self.velocity_range[0]

        # 计算频率偏移因子
        c_km_s = 299792.458  # 光速 km/s
        freq_shift_factor = 1 + velocity / c_km_s

        # 获取图像尺寸
        channels, height, width = img.shape

        # 创建新的频率索引网格
        # 原始频率索引：0, 1, 2, ..., height-1
        old_indices = torch.arange(height, dtype=torch.float32, device=img.device)

        # 应用频率偏移：新索引 = 原索引 * 偏移因子
        new_indices = old_indices * freq_shift_factor

        # 限制在有效范围内，防止越界
        new_indices = torch.clamp(new_indices, 0, height - 1)

        result = torch.zeros_like(img)

        # 对每个通道和每个时间点进行插值
        for c in range(channels):
            for w in range(width):
                # 提取当前时间点的频率剖面
                freq_profile = img[c, :, w]  # [height]

                # 使用线性插值重新采样
                # 对每个新的频率索引位置进行插值
                for new_freq_idx in range(height):
                    target_pos = new_indices[new_freq_idx].item()

                    # 找到插值的两个邻近点
                    lower_idx = int(torch.floor(torch.tensor(target_pos)).item())
                    upper_idx = min(lower_idx + 1, height - 1)

                    # 计算插值权重
                    if lower_idx == upper_idx:
                        # 边界情况：直接使用该点的值
                        result[c, new_freq_idx, w] = freq_profile[lower_idx]
                    else:
                        # 线性插值
                        weight = target_pos - lower_idx
                        result[c, new_freq_idx, w] = (
                            (1 - weight) * freq_profile[lower_idx] +
                            weight * freq_profile[upper_idx]
                        )

        return result


class AdaptiveNormalization:
    """
    自适应归一化
    保持信号的相对强度分布，同时标准化动态范围
    """
    
    def __init__(self, target_mean: float = 0.0, target_std: float = 1.0, eps: float = 1e-8):
        self.target_mean = target_mean
        self.target_std = target_std
        self.eps = eps
    
    def __call__(self, img: Tensor) -> Tensor:
        # 计算当前统计量
        current_mean = img.mean()
        current_std = img.std()
        
        # 避免除零
        if current_std < self.eps:
            return img
        
        # 标准化
        normalized = (img - current_mean) / current_std
        
        # 重新缩放到目标分布
        return normalized * self.target_std + self.target_mean


class PhysicsConstrainedCompose:
    """
    物理约束的变换组合器
    确保所有变换都符合脉冲星物理学原理
    """
    
    def __init__(self, transforms_list: list):
        self.transforms = transforms_list
    
    def __call__(self, img: Tensor) -> Tensor:
        for t in self.transforms:
            img = t(img)
        return img


def create_physics_constrained_transforms(
    mode: str = 'train',
    config: Optional[Dict[str, Any]] = None
) -> Callable:
    """
    创建物理约束的数据变换
    
    Args:
        mode: 'train' 或 'val'
        config: 变换配置字典
        
    Returns:
        变换函数
    """
    if config is None:
        config = {}
    
    # 基础变换（总是应用）
    base_transforms = []
    
    # 转换为张量
    if config.get('to_tensor', True):
        base_transforms.append(transforms.ToTensor())
    
    if mode == 'train':
        # 训练时的物理约束增强
        augment_transforms = []
        
        # 相位旋转（物理合理）
        if config.get('use_rotation', True):
            rotation_degrees = config.get('rotation_degrees', 180)
            augment_transforms.append(PhysicsConstrainedRotation(rotation_degrees))
        
        # 翻转变换（谨慎使用）
        if config.get('use_flip', True):
            h_prob = config.get('horizontal_flip_prob', 0.5)
            v_prob = config.get('vertical_flip_prob', 0.0)  # 默认不使用垂直翻转
            augment_transforms.append(PhysicsConstrainedFlip(h_prob, v_prob))
        
        # 相位偏移（物理合理）
        if config.get('use_phase_shift', True):
            max_shift = config.get('max_phase_shift_ratio', 0.1)
            augment_transforms.append(PhaseShift(max_shift))
        
        # 观测噪声（物理合理）
        if config.get('use_noise', True):
            noise_std = config.get('noise_std', 0.01)
            snr_range = config.get('snr_range', (0.8, 1.2))
            augment_transforms.append(ObservationalNoise(noise_std, snr_range))
        
        # 强度缩放（物理合理）
        if config.get('use_intensity_scaling', True):
            scale_range = config.get('intensity_scale_range', (0.8, 1.2))
            augment_transforms.append(IntensityScaling(scale_range))

        # 色散延迟变换（物理合理）
        if config.get('use_dispersion_delay', True):
            dm_variation_range = config.get('dm_variation_range', (-0.1, 0.1))
            dispersion_probability = config.get('dispersion_probability', 0.5)
            augment_transforms.append(DispersionDelayTransform(dm_variation_range, dispersion_probability))

        # 散射效应变换（物理合理）
        if config.get('use_scattering', True):
            tau_scatter_range = config.get('tau_scatter_range', (0.1, 2.0))
            scattering_probability = config.get('scattering_probability', 0.3)
            augment_transforms.append(ScatteringTransform(tau_scatter_range, scattering_probability))

        # 多普勒效应变换（物理合理）
        if config.get('use_doppler_shift', True):
            velocity_range = config.get('velocity_range', (-30, 30))
            doppler_probability = config.get('doppler_probability', 0.4)
            augment_transforms.append(DopplerShiftTransform(velocity_range, doppler_probability))

        # 组合所有变换
        all_transforms = base_transforms + augment_transforms
    else:
        # 验证/测试时只使用基础变换
        all_transforms = base_transforms
    
    # 自适应归一化（总是最后应用）
    if config.get('use_adaptive_norm', True):
        target_mean = config.get('target_mean', 0.0)
        target_std = config.get('target_std', 1.0)
        all_transforms.append(AdaptiveNormalization(target_mean, target_std))
    
    return transforms.Compose(all_transforms)


def validate_physics_constraints(transform_func: Callable, sample_data: Tensor, transform_name: str = "Unknown") -> bool:
    """
    验证数据变换是否符合物理约束

    增强版本（v2.0）：
    - 添加了频率域连续性检查（针对多普勒效应变换）
    - 添加了动态范围检查（确保信号动态范围合理）
    - 添加了时间域连续性检查（针对色散延迟变换）
    - 保持向后兼容性，所有现有验证逻辑不变

    Args:
        transform_func: 变换函数
        sample_data: 样本数据
        transform_name: 变换名称，用于调试信息

    Returns:
        是否符合物理约束
    """
    try:
        # 应用变换
        transformed = transform_func(sample_data)

        # 基本约束检查（保持原有逻辑）
        if transformed.shape != sample_data.shape:
            return False

        # 数值有效性检查（保持原有逻辑）
        if torch.isnan(transformed).any() or torch.isinf(transformed).any():
            return False

        # 能量守恒检查（保持原有逻辑，但放宽限制）
        original_energy = torch.sum(sample_data ** 2)
        transformed_energy = torch.sum(transformed ** 2)

        if original_energy > 0:
            energy_ratio = transformed_energy / original_energy
            # 放宽能量变化限制，考虑散射等物理效应可能导致的能量损失
            if energy_ratio < 0.05 or energy_ratio > 20.0:
                return False

        # 新增：动态范围检查
        if not _check_dynamic_range(transformed, sample_data):
            return False

        # 新增：频率域连续性检查（针对多频率通道数据）
        if transformed.dim() >= 3 and transformed.shape[-2] > 1:
            if not _check_frequency_continuity(transformed):
                return False

        # 新增：时间域连续性检查（针对时间序列数据）
        if transformed.dim() >= 2 and transformed.shape[-1] > 1:
            if not _check_temporal_continuity(transformed):
                return False

        return True

    except Exception as e:
        # 在调试模式下可以打印错误信息
        # print(f"Physics constraint validation failed for {transform_name}: {e}")
        return False


def _check_dynamic_range(transformed: Tensor, original: Tensor) -> bool:
    """
    检查动态范围是否合理

    Args:
        transformed: 变换后的张量
        original: 原始张量

    Returns:
        动态范围是否合理
    """
    # 计算动态范围
    transformed_range = torch.max(transformed) - torch.min(transformed)
    original_range = torch.max(original) - torch.min(original)

    # 动态范围不应该为零（除非原始数据就是常数）
    if transformed_range < 1e-8 and original_range > 1e-6:
        return False

    # 动态范围变化不应该过于极端
    if original_range > 1e-8:
        range_ratio = transformed_range / original_range
        if range_ratio < 0.01 or range_ratio > 100.0:
            return False

    return True


def _check_frequency_continuity(tensor: Tensor) -> bool:
    """
    检查频率域连续性（针对多普勒效应等频率相关变换）

    Args:
        tensor: 输入张量，假设形状为 [..., freq_channels, time_samples]

    Returns:
        频率域是否连续
    """
    # 计算频率方向的平均强度
    freq_profile = torch.mean(tensor, dim=-1)  # 在时间维度上平均

    # 计算频率间的差分
    if freq_profile.shape[-1] > 2:
        freq_diff = torch.diff(freq_profile, dim=-1)

        # 检查是否存在异常的跳跃
        diff_std = torch.std(freq_diff)
        diff_mean = torch.mean(torch.abs(freq_diff))

        # 如果标准差远大于平均值，可能存在不连续性
        if diff_std > diff_mean * 10 and diff_mean > 1e-6:
            return False

    return True


def _check_temporal_continuity(tensor: Tensor) -> bool:
    """
    检查时间域连续性（针对色散延迟等时间相关变换）

    Args:
        tensor: 输入张量，假设最后一个维度是时间

    Returns:
        时间域是否连续
    """
    # 对于每个频率通道，检查时间序列的连续性
    if tensor.dim() >= 3:
        # 多频率通道情况
        for freq_idx in range(min(tensor.shape[-2], 5)):  # 只检查前5个频率通道
            time_series = tensor[..., freq_idx, :]
            if not _check_single_time_series_continuity(time_series):
                return False
    else:
        # 单频率通道情况
        if not _check_single_time_series_continuity(tensor):
            return False

    return True


def _check_single_time_series_continuity(time_series: Tensor) -> bool:
    """
    检查单个时间序列的连续性

    Args:
        time_series: 时间序列张量

    Returns:
        时间序列是否连续
    """
    if time_series.shape[-1] <= 2:
        return True

    # 计算时间方向的差分
    time_diff = torch.diff(time_series, dim=-1)

    # 检查是否存在异常的跳跃
    if time_diff.numel() > 0:
        diff_std = torch.std(time_diff)
        diff_mean = torch.mean(torch.abs(time_diff))

        # 如果存在极端的跳跃，可能表示不连续
        max_diff = torch.max(torch.abs(time_diff))
        if diff_mean > 1e-6 and max_diff > diff_mean * 50:
            return False

    return True


def test_physics_transforms(verbose: bool = False) -> bool:
    """
    全面测试所有物理变换的正确性和兼容性

    测试内容：
    1. 单独测试每个新变换（DispersionDelayTransform、ScatteringTransform、DopplerShiftTransform）
    2. 组合变换兼容性测试
    3. 物理合理性验证
    4. 边界条件处理
    5. 性能基准测试

    Args:
        verbose: 是否输出详细测试信息

    Returns:
        所有测试是否通过
    """
    if verbose:
        print("🧪 开始全面物理变换测试...")

    try:
        # 创建测试数据
        test_data = torch.randn(1, 64, 64)

        # 测试1：单独测试每个新变换
        if not _test_individual_transforms(test_data, verbose):
            return False

        # 测试2：组合变换兼容性测试
        if not _test_transform_combinations(test_data, verbose):
            return False

        # 测试3：边界条件测试
        if not _test_edge_cases(verbose):
            return False

        # 测试4：性能基准测试
        if not _test_performance_benchmarks(test_data, verbose):
            return False

        if verbose:
            print("✅ 所有物理变换测试通过！")

        return True

    except Exception as e:
        if verbose:
            print(f"❌ 物理变换测试失败: {e}")
        return False


def _test_individual_transforms(test_data: Tensor, verbose: bool) -> bool:
    """测试单个变换的功能"""
    transforms_to_test = [
        ("DispersionDelayTransform", DispersionDelayTransform(probability=1.0)),
        ("ScatteringTransform", ScatteringTransform(probability=1.0)),
        ("DopplerShiftTransform", DopplerShiftTransform(probability=1.0))
    ]

    for name, transform in transforms_to_test:
        if verbose:
            print(f"  测试 {name}...")

        # 基本功能测试
        result = transform(test_data.clone())

        # 形状检查
        if result.shape != test_data.shape:
            if verbose:
                print(f"    ❌ {name}: 形状不匹配")
            return False

        # 数值有效性检查
        if torch.isnan(result).any() or torch.isinf(result).any():
            if verbose:
                print(f"    ❌ {name}: 包含无效数值")
            return False

        # 物理约束验证
        if not validate_physics_constraints(lambda x: transform(x), test_data, name):
            if verbose:
                print(f"    ❌ {name}: 物理约束验证失败")
            return False

        if verbose:
            print(f"    ✅ {name}: 测试通过")

    return True


def _test_transform_combinations(test_data: Tensor, verbose: bool) -> bool:
    """测试变换组合的兼容性"""
    if verbose:
        print("  测试变换组合兼容性...")

    # 测试所有配置级别
    for config_name in ['conservative', 'moderate', 'aggressive']:
        config = PHYSICS_CONSTRAINED_CONFIGS[config_name].copy()
        config['to_tensor'] = False  # 避免ToTensor问题

        try:
            transforms = create_physics_constrained_transforms(
                mode='train',
                config=config
            )

            result = transforms(test_data.clone())

            # 基本验证
            if torch.isnan(result).any() or torch.isinf(result).any():
                if verbose:
                    print(f"    ❌ {config_name}: 组合变换产生无效数值")
                return False

            if verbose:
                print(f"    ✅ {config_name}: 组合变换测试通过")

        except Exception as e:
            if verbose:
                print(f"    ❌ {config_name}: 组合变换失败 - {e}")
            return False

    return True


def _test_edge_cases(verbose: bool) -> bool:
    """测试边界条件"""
    if verbose:
        print("  测试边界条件...")

    edge_cases = [
        ("零张量", torch.zeros(1, 32, 32)),
        ("常数张量", torch.ones(1, 32, 32) * 5.0),
        ("小尺寸张量", torch.randn(1, 8, 8)),
        ("单像素张量", torch.randn(1, 1, 1)),
        ("极值张量", torch.randn(1, 16, 16) * 1000)
    ]

    transforms = [
        DispersionDelayTransform(probability=1.0),
        ScatteringTransform(probability=1.0),
        DopplerShiftTransform(probability=1.0)
    ]

    for case_name, test_tensor in edge_cases:
        for transform in transforms:
            try:
                result = transform(test_tensor.clone())

                # 基本检查
                if result.shape != test_tensor.shape:
                    if verbose:
                        print(f"    ❌ {case_name}: 形状不匹配")
                    return False

                if torch.isnan(result).any() or torch.isinf(result).any():
                    if verbose:
                        print(f"    ❌ {case_name}: 包含无效数值")
                    return False

            except Exception as e:
                if verbose:
                    print(f"    ❌ {case_name}: 异常 - {e}")
                return False

    if verbose:
        print("    ✅ 边界条件测试通过")

    return True


def _test_performance_benchmarks(test_data: Tensor, verbose: bool) -> bool:
    """测试性能基准"""
    if verbose:
        print("  测试性能基准...")

    import time

    # 基准测试：无变换
    start_time = time.time()
    for _ in range(100):
        _ = test_data.clone()
    baseline_time = time.time() - start_time

    # 测试新变换的性能开销
    transforms = [
        ("DispersionDelayTransform", DispersionDelayTransform(probability=1.0)),
        ("ScatteringTransform", ScatteringTransform(probability=1.0)),
        ("DopplerShiftTransform", DopplerShiftTransform(probability=1.0))
    ]

    for name, transform in transforms:
        start_time = time.time()
        for _ in range(100):
            _ = transform(test_data.clone())
        transform_time = time.time() - start_time

        # 计算性能开销
        if baseline_time > 0:
            overhead = (transform_time - baseline_time) / baseline_time * 100
        else:
            overhead = 0  # 基准时间太短，无法计算开销

        if verbose:
            print(f"    {name}: {overhead:.1f}% 性能开销")

        # 检查性能开销是否在可接受范围内（<500%）
        if overhead > 500:  # 允许较大的开销，因为这些是复杂的物理变换
            if verbose:
                print(f"    ⚠️ {name}: 性能开销过高 ({overhead:.1f}%)")

    if verbose:
        print("    ✅ 性能基准测试完成")

    return True


# 预定义的物理约束变换配置
PHYSICS_CONSTRAINED_CONFIGS = {
    'conservative': {
        'use_rotation': True,
        'rotation_degrees': 90,
        'use_flip': True,
        'horizontal_flip_prob': 0.3,
        'vertical_flip_prob': 0.0,
        'use_phase_shift': True,
        'max_phase_shift_ratio': 0.05,
        'use_noise': True,
        'noise_std': 0.005,
        'snr_range': (0.9, 1.1),
        'use_intensity_scaling': True,
        'intensity_scale_range': (0.9, 1.1),
        'use_dispersion_delay': True,
        'dm_variation_range': (-0.05, 0.05),
        'dispersion_probability': 0.3,
        'use_scattering': True,
        'tau_scatter_range': (0.1, 1.0),
        'scattering_probability': 0.2,
        'use_doppler_shift': True,
        'velocity_range': (-15, 15),
        'doppler_probability': 0.3,
        'use_adaptive_norm': True,
        'target_mean': 0.0,
        'target_std': 1.0
    },
    'moderate': {
        'use_rotation': True,
        'rotation_degrees': 180,
        'use_flip': True,
        'horizontal_flip_prob': 0.5,
        'vertical_flip_prob': 0.0,
        'use_phase_shift': True,
        'max_phase_shift_ratio': 0.1,
        'use_noise': True,
        'noise_std': 0.01,
        'snr_range': (0.8, 1.2),
        'use_intensity_scaling': True,
        'intensity_scale_range': (0.8, 1.2),
        'use_dispersion_delay': True,
        'dm_variation_range': (-0.1, 0.1),
        'dispersion_probability': 0.5,
        'use_scattering': True,
        'tau_scatter_range': (0.1, 2.0),
        'scattering_probability': 0.3,
        'use_doppler_shift': True,
        'velocity_range': (-30, 30),
        'doppler_probability': 0.4,
        'use_adaptive_norm': True,
        'target_mean': 0.0,
        'target_std': 1.0
    },
    'aggressive': {
        'use_rotation': True,
        'rotation_degrees': 180,
        'use_flip': True,
        'horizontal_flip_prob': 0.7,
        'vertical_flip_prob': 0.1,
        'use_phase_shift': True,
        'max_phase_shift_ratio': 0.15,
        'use_noise': True,
        'noise_std': 0.02,
        'snr_range': (0.7, 1.3),
        'use_intensity_scaling': True,
        'intensity_scale_range': (0.7, 1.3),
        'use_dispersion_delay': True,
        'dm_variation_range': (-0.15, 0.15),
        'dispersion_probability': 0.7,
        'use_scattering': True,
        'tau_scatter_range': (0.1, 3.0),
        'scattering_probability': 0.5,
        'use_doppler_shift': True,
        'velocity_range': (-50, 50),
        'doppler_probability': 0.6,
        'use_adaptive_norm': True,
        'target_mean': 0.0,
        'target_std': 1.0
    }
}


# 导出的公共接口
__all__ = [
    'PhysicsConstrainedRotation',
    'PhysicsConstrainedFlip',
    'ObservationalNoise',
    'PhaseShift',
    'IntensityScaling',
    'DispersionDelayTransform',
    'ScatteringTransform',
    'DopplerShiftTransform',
    'AdaptiveNormalization',
    'PhysicsConstrainedCompose',
    'create_physics_constrained_transforms',
    'validate_physics_constraints',
    'PHYSICS_CONSTRAINED_CONFIGS'
]
