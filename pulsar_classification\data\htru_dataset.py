"""
HTRU数据集处理模块
专门化的脉冲星数据集类，支持FPP/TPP模态

HTRU数据集特点：
- 包含真实脉冲星和非脉冲星样本
- 支持FPP (Frequency-Phase Plot) 和 TPP (Time-Phase Plot) 模态
- 64×64像素的灰度图像
- 二分类任务：脉冲星 vs 非脉冲星

物理意义：
- FPP: 频率-相位图，反映色散效应
- TPP: 时间-相位图，反映周期演化
"""

import os
import glob
from typing import Optional, Callable, Tuple, List, Dict, Any, Union
import torch
from torch.utils.data import Dataset, DataLoader
from PIL import Image
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split

from .transforms import create_physics_constrained_transforms, PHYSICS_CONSTRAINED_CONFIGS


class HTRUDataset(Dataset):
    """
    HTRU脉冲星数据集
    
    支持FPP和TPP两种模态的脉冲星分类数据
    
    Args:
        data_root: 数据根目录
        split: 数据分割 ('train', 'val', 'test')
        modality: 数据模态 ('FPP', 'TPP', 'both')
        transform: 数据变换函数
        target_transform: 标签变换函数
        download: 是否下载数据（如果不存在）
    """
    
    def __init__(
        self,
        data_root: str,
        split: str = 'train',
        modality: str = 'FPP',
        transform: Optional[Callable] = None,
        target_transform: Optional[Callable] = None,
        download: bool = False
    ):
        self.data_root = data_root
        self.split = split
        self.modality = modality
        self.transform = transform
        self.target_transform = target_transform
        
        # 验证参数
        if split not in ['train', 'val', 'test']:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")
        
        if modality not in ['FPP', 'TPP', 'both']:
            raise ValueError(f"Invalid modality: {modality}. Must be 'FPP', 'TPP', or 'both'")
        
        # 检查数据目录
        if not os.path.exists(data_root):
            if download:
                self._download_data()
            else:
                raise FileNotFoundError(f"Data directory not found: {data_root}")
        
        # 加载数据
        self.samples = self._load_samples()
        
        # 类别信息
        self.classes = ['non_pulsar', 'pulsar']
        self.class_to_idx = {cls: idx for idx, cls in enumerate(self.classes)}
        
    def _load_samples(self) -> List[Tuple[str, int]]:
        """加载样本路径和标签"""
        samples = []

        # 构建数据路径
        if self.modality == 'both':
            modalities = ['FPP', 'TPP']
        else:
            modalities = [self.modality]

        for mod in modalities:
            # 修改split名称映射
            split_name = 'validation' if self.split == 'val' else self.split
            mod_path = os.path.join(self.data_root, mod, split_name)

            if not os.path.exists(mod_path):
                continue

            # 加载所有.npy文件并根据文件名判断标签
            npy_files = glob.glob(os.path.join(mod_path, '*.npy'))

            for file_path in npy_files:
                filename = os.path.basename(file_path)

                # 根据文件名判断标签
                if 'positive' in filename or 'pulsar' in filename:
                    label = 1  # 脉冲星
                elif 'negative' in filename or 'cand' in filename:
                    label = 0  # 非脉冲星
                else:
                    # 如果无法从文件名判断，跳过
                    continue

                samples.append((file_path, label))

        if len(samples) == 0:
            raise RuntimeError(f"No samples found in {self.data_root} for split {self.split}")

        return samples
    
    def _download_data(self):
        """下载HTRU数据集（占位符实现）"""
        # 这里应该实现实际的数据下载逻辑
        # 由于HTRU数据集的具体下载方式可能因版本而异，这里提供框架
        print(f"Downloading HTRU dataset to {self.data_root}...")
        print("Please manually download the HTRU dataset and organize it as:")
        print("data_root/")
        print("  ├── FPP/")
        print("  │   ├── train/")
        print("  │   │   ├── pulsar/")
        print("  │   │   └── non_pulsar/")
        print("  │   ├── val/")
        print("  │   └── test/")
        print("  └── TPP/")
        print("      ├── train/")
        print("      ├── val/")
        print("      └── test/")
        raise NotImplementedError("Automatic download not implemented. Please download manually.")
    
    def __len__(self) -> int:
        return len(self.samples)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int]:
        """获取单个样本"""
        if idx < 0 or idx >= len(self.samples):
            raise IndexError(f"Index {idx} out of range for dataset of size {len(self.samples)}")

        img_path, label = self.samples[idx]

        # 加载.npy文件
        try:
            image = np.load(img_path)  # 加载numpy数组
        except Exception as e:
            raise RuntimeError(f"Error loading image {img_path}: {e}")

        # 确保数据格式正确
        if image.ndim == 3 and image.shape[2] == 1:
            # 从(H, W, 1)转换为(H, W)
            image = image.squeeze(2)
        elif image.ndim == 3 and image.shape[0] == 1:
            # 从(1, H, W)转换为(H, W)
            image = image.squeeze(0)
        elif image.ndim != 2:
            raise ValueError(f"Unexpected image shape: {image.shape}. Expected 2D or 3D with single channel.")

        # 确保数据类型和范围
        image = image.astype(np.float32)

        # 数值范围检查和归一化
        if np.isnan(image).any() or np.isinf(image).any():
            raise ValueError(f"Image contains NaN or Inf values: {img_path}")

        # 归一化处理
        if image.max() > 1.0:
            image = image / 255.0

        # 确保值在合理范围内
        image = np.clip(image, 0.0, 1.0)

        # 转换为PIL图像以便应用变换
        image_pil = Image.fromarray((image * 255).astype(np.uint8), mode='L')

        # 应用变换
        if self.transform is not None:
            try:
                image = self.transform(image_pil)
            except Exception as e:
                raise RuntimeError(f"Error applying transform to image {img_path}: {e}")
        else:
            # 如果没有变换，手动转换为张量
            image = torch.from_numpy(image).unsqueeze(0)  # 添加通道维度 [1, H, W]

        # 确保输出张量格式正确
        if not isinstance(image, torch.Tensor):
            raise TypeError(f"Transform should return torch.Tensor, got {type(image)}")

        if image.dim() != 3 or image.size(0) != 1:
            raise ValueError(f"Expected output tensor shape [1, H, W], got {image.shape}")

        # 应用标签变换
        if self.target_transform is not None:
            label = self.target_transform(label)

        return image, label
    
    def get_class_distribution(self) -> Dict[str, int]:
        """获取类别分布"""
        distribution = {cls: 0 for cls in self.classes}
        
        for _, label in self.samples:
            class_name = self.classes[label]
            distribution[class_name] += 1
        
        return distribution
    
    def get_sample_info(self, idx: int) -> Dict[str, Any]:
        """获取样本信息"""
        img_path, label = self.samples[idx]
        
        return {
            'path': img_path,
            'label': label,
            'class_name': self.classes[label],
            'modality': self._get_modality_from_path(img_path),
            'split': self.split
        }
    
    def _get_modality_from_path(self, path: str) -> str:
        """从路径推断模态"""
        if 'FPP' in path:
            return 'FPP'
        elif 'TPP' in path:
            return 'TPP'
        else:
            return 'unknown'


def create_htru_datasets(
    data_root: str,
    modality: str = 'FPP',
    transform_config: Optional[str] = 'moderate',
    val_split: float = 0.2,
    test_split: float = 0.1,
    random_state: int = 42
) -> Tuple[HTRUDataset, HTRUDataset, HTRUDataset]:
    """
    创建HTRU训练、验证和测试数据集
    
    Args:
        data_root: 数据根目录
        modality: 数据模态
        transform_config: 变换配置名称
        val_split: 验证集比例
        test_split: 测试集比例
        random_state: 随机种子
        
    Returns:
        (train_dataset, val_dataset, test_dataset)
    """
    # 创建变换
    if transform_config in PHYSICS_CONSTRAINED_CONFIGS:
        train_transform = create_physics_constrained_transforms(
            mode='train',
            config=PHYSICS_CONSTRAINED_CONFIGS[transform_config]
        )
        val_transform = create_physics_constrained_transforms(
            mode='val',
            config=PHYSICS_CONSTRAINED_CONFIGS[transform_config]
        )
    else:
        train_transform = create_physics_constrained_transforms(mode='train')
        val_transform = create_physics_constrained_transforms(mode='val')
    
    # 创建数据集
    train_dataset = HTRUDataset(
        data_root=data_root,
        split='train',
        modality=modality,
        transform=train_transform
    )
    
    val_dataset = HTRUDataset(
        data_root=data_root,
        split='val',
        modality=modality,
        transform=val_transform
    )
    
    test_dataset = HTRUDataset(
        data_root=data_root,
        split='test',
        modality=modality,
        transform=val_transform
    )
    
    return train_dataset, val_dataset, test_dataset


def create_htru_dataloaders(
    datasets: Tuple[HTRUDataset, HTRUDataset, HTRUDataset],
    batch_size: int = 32,
    num_workers: int = 4,
    pin_memory: bool = True
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    创建HTRU数据加载器
    
    Args:
        datasets: (train_dataset, val_dataset, test_dataset)
        batch_size: 批大小
        num_workers: 工作进程数
        pin_memory: 是否固定内存
        
    Returns:
        (train_loader, val_loader, test_loader)
    """
    train_dataset, val_dataset, test_dataset = datasets
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory
    )
    
    return train_loader, val_loader, test_loader


# 导出的公共接口
__all__ = [
    'HTRUDataset',
    'create_htru_datasets',
    'create_htru_dataloaders'
]
