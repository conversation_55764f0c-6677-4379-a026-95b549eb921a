"""
专门化的脉冲星Res2Net模型
基于CifarRes2Net架构，集成混合通道适配器和SE注意力机制

理论基础：
- 多尺度特征提取：Res2Net的层次化处理适合脉冲星信号的多时间尺度特性
- 通道适配：物理约束的1→3通道转换
- 注意力机制：SE模块增强特征表达能力

目标性能：94-97%准确率的baseline
"""

from typing import List, Optional, Dict, Any, Union
import torch
import torch.nn as nn
from torch import Tensor

from .channel_adapters import create_channel_adapter
from .attention import create_attention_module


def conv3x3(in_planes: int, out_planes: int, stride: int = 1, groups: int = 1) -> nn.Conv2d:
    """3x3卷积"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=3, stride=stride,
                     padding=1, groups=groups, bias=False)


def conv1x1(in_planes: int, out_planes: int, stride: int = 1) -> nn.Conv2d:
    """1x1卷积"""
    return nn.Conv2d(in_planes, out_planes, kernel_size=1, stride=stride, bias=False)


class Res2NetBottleneck(nn.Module):
    """
    Res2Net瓶颈块
    实现多尺度特征提取的核心组件

    数学表述：
    - 分组处理：X = [X₁, X₂, ..., Xₛ]
    - 层次化融合：Yᵢ = Fᵢ(Xᵢ + Yᵢ₋₁)
    - 感受野增强：RF_eff = RF_base × (1 + (s-1) × γ)
    """
    expansion = 4

    def __init__(
        self,
        inplanes: int,
        planes: int,
        downsample: Optional[nn.Module] = None,
        stride: int = 1,
        scales: int = 4,
        groups: int = 1,
        attention_type: str = 'se',
        enhanced_attention_config: Optional[Dict[str, Any]] = None,
        norm_layer: Optional[nn.Module] = None
    ):
        super(Res2NetBottleneck, self).__init__()

        if planes % scales != 0:
            raise ValueError('Planes must be divisible by scales')
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d

        bottleneck_planes = groups * planes
        self.conv1 = conv1x1(inplanes, bottleneck_planes, stride)
        self.bn1 = norm_layer(bottleneck_planes)

        # Res2Net多尺度处理
        self.conv2 = nn.ModuleList([
            conv3x3(bottleneck_planes // scales, bottleneck_planes // scales, groups=groups)
            for _ in range(scales-1)
        ])
        self.bn2 = nn.ModuleList([
            norm_layer(bottleneck_planes // scales)
            for _ in range(scales-1)
        ])

        self.conv3 = conv1x1(bottleneck_planes, planes * self.expansion)
        self.bn3 = norm_layer(planes * self.expansion)

        self.relu = nn.ReLU(inplace=True)
        self.downsample = downsample
        self.stride = stride
        self.scales = scales

        # 注意力机制
        self.attention = create_attention_module(
            attention_type,
            planes * self.expansion,
            enhanced_attention_config
        )

    def forward(self, x: Tensor) -> Tensor:
        identity = x

        out = self.conv1(x)
        out = self.bn1(out)
        out = self.relu(out)

        # Res2Net多尺度处理
        xs = torch.chunk(out, self.scales, 1)
        ys = []
        for s in range(self.scales):
            if s == 0:
                ys.append(xs[s])
            elif s == 1:
                ys.append(self.relu(self.bn2[s-1](self.conv2[s-1](xs[s]))))
            else:
                ys.append(self.relu(self.bn2[s-1](self.conv2[s-1](xs[s] + ys[-1]))))
        out = torch.cat(ys, 1)

        out = self.conv3(out)
        out = self.bn3(out)

        # 注意力机制
        out = self.attention(out)

        if self.downsample is not None:
            identity = self.downsample(x)

        out += identity
        out = self.relu(out)

        return out


class PulsarRes2Net(nn.Module):
    """
    专门化的脉冲星Res2Net模型

    集成混合通道适配器、多尺度特征提取和注意力机制
    目标：94-97%准确率的baseline性能
    """

    def __init__(
        self,
        layers: List[int] = [3, 4, 6],
        num_classes: int = 2,
        zero_init_residual: bool = False,
        groups: int = 1,
        width: int = 64,
        scales: int = 4,
        attention_type: str = 'se',
        enhanced_attention_config: Optional[Dict[str, Any]] = None,
        norm_layer: Optional[nn.Module] = None,
        channel_adapter_config: Optional[Dict[str, Any]] = None,
        dropout_rate: float = 0.1
    ):
        super(PulsarRes2Net, self).__init__()

        if norm_layer is None:
            norm_layer = nn.BatchNorm2d

        # 通道适配器配置
        if channel_adapter_config is None:
            channel_adapter_config = {
                'type': 'physics_constrained',
                'use_learnable': True,
                'use_edge_features': True,
                'edge_kernel_type': 'sobel'
            }

        # 通道适配器
        self.channel_adapter = create_channel_adapter(channel_adapter_config)

        # 存储增强注意力配置
        self.enhanced_attention_config = enhanced_attention_config

        # 网络架构参数
        planes = [int(width * scales * 2 ** i) for i in range(len(layers))]
        self.inplanes = planes[0]

        # 第一层：适配64×64输入
        self.conv1 = conv3x3(3, planes[0])
        self.bn1 = norm_layer(planes[0])
        self.relu = nn.ReLU(inplace=True)

        # Res2Net层
        self.layer1 = self._make_layer(Res2NetBottleneck, planes[0], layers[0],
                                      scales=scales, groups=groups,
                                      attention_type=attention_type, norm_layer=norm_layer)
        self.layer2 = self._make_layer(Res2NetBottleneck, planes[1], layers[1], stride=2,
                                      scales=scales, groups=groups,
                                      attention_type=attention_type, norm_layer=norm_layer)
        self.layer3 = self._make_layer(Res2NetBottleneck, planes[2], layers[2], stride=2,
                                      scales=scales, groups=groups,
                                      attention_type=attention_type, norm_layer=norm_layer)

        # 分类头
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        self.dropout = nn.Dropout(dropout_rate)
        self.fc = nn.Linear(planes[2] * Res2NetBottleneck.expansion, num_classes)

        # 权重初始化
        self._initialize_weights(zero_init_residual)

    def _make_layer(
        self,
        block: nn.Module,
        planes: int,
        blocks: int,
        stride: int = 1,
        scales: int = 4,
        groups: int = 1,
        attention_type: str = 'se',
        norm_layer: Optional[nn.Module] = None
    ) -> nn.Sequential:
        if norm_layer is None:
            norm_layer = nn.BatchNorm2d

        downsample = None
        if stride != 1 or self.inplanes != planes * block.expansion:
            downsample = nn.Sequential(
                conv1x1(self.inplanes, planes * block.expansion, stride),
                norm_layer(planes * block.expansion),
            )

        layers = []
        layers.append(block(self.inplanes, planes, downsample, stride=stride,
                           scales=scales, groups=groups,
                           attention_type=attention_type,
                           enhanced_attention_config=self.enhanced_attention_config,
                           norm_layer=norm_layer))
        self.inplanes = planes * block.expansion

        for _ in range(1, blocks):
            layers.append(block(self.inplanes, planes, scales=scales,
                               groups=groups, attention_type=attention_type,
                               enhanced_attention_config=self.enhanced_attention_config,
                               norm_layer=norm_layer))

        return nn.Sequential(*layers)

    def _initialize_weights(self, zero_init_residual: bool = False):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            elif isinstance(m, (nn.BatchNorm2d, nn.GroupNorm)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

        # 零初始化残差分支
        if zero_init_residual:
            for m in self.modules():
                if isinstance(m, Res2NetBottleneck):
                    nn.init.constant_(m.bn3.weight, 0)

    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播

        Args:
            x: 输入张量 [B, 1, 64, 64]

        Returns:
            分类logits [B, num_classes]
        """
        # 输入通道适配：1通道 -> 3通道
        x = self.channel_adapter(x)

        # 特征提取
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)

        x = self.layer1(x)
        x = self.layer2(x)
        x = self.layer3(x)

        # 分类
        x = self.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.dropout(x)
        x = self.fc(x)

        return x

    def get_channel_adapter_weights(self) -> Optional[Dict[str, float]]:
        """获取通道适配器的融合权重"""
        if hasattr(self.channel_adapter, 'get_weight_info'):
            return self.channel_adapter.get_weight_info()
        return None


# 导出的公共接口
__all__ = [
    'Res2NetBottleneck',
    'PulsarRes2Net'
]
