"""
SE注意力机制模块
实现Squeeze-and-Excitation注意力机制，增强特征表达能力

基于论文：Squeeze-and-Excitation Networks (CVPR 2018)
数学表述：
- Squeeze: z = GAP(F) = (1/HW) ∑ᵢ₌₁ᴴ ∑ⱼ₌₁ᵂ F(i,j)
- Excitation: s = σ(W₂ · ReLU(W₁ · z))
- Scale: F' = s ⊙ F
"""

from typing import Optional, Dict, Any
import torch
import torch.nn as nn
from torch import Tensor

# 在现有导入后添加
from .attention_modules import EMAModule, SimAMModule


class SEModule(nn.Module):
    """
    Squeeze-and-Excitation模块

    通过全局平均池化和两个全连接层学习通道间的依赖关系，
    自适应地重新校准通道特征响应。

    Args:
        channels: 输入通道数
        reduction: 降维比例，控制中间层的通道数 (default: 16)
        activation: 激活函数类型 (default: ReLU)
        gate_activation: 门控激活函数 (default: Sigmoid)
    """

    def __init__(
        self,
        channels: int,
        reduction: int = 16,
        activation: nn.Module = nn.ReLU,
        gate_activation: nn.Module = nn.Sigmoid
    ):
        super(SEModule, self).__init__()

        if channels < reduction:
            raise ValueError(f"channels ({channels}) must be >= reduction ({reduction})")

        reduced_channels = max(channels // reduction, 1)

        # Squeeze: 全局平均池化
        self.avg_pool = nn.AdaptiveAvgPool2d(1)

        # Excitation: 两个全连接层
        self.fc1 = nn.Conv2d(channels, reduced_channels, kernel_size=1, padding=0, bias=True)
        self.activation = activation(inplace=True)
        self.fc2 = nn.Conv2d(reduced_channels, channels, kernel_size=1, padding=0, bias=True)
        self.gate_activation = gate_activation()

        # 权重初始化
        self._initialize_weights()

    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播

        Args:
            x: 输入特征图 [B, C, H, W]

        Returns:
            重新校准后的特征图 [B, C, H, W]
        """
        # 保存输入用于残差连接
        input_tensor = x

        # Squeeze: 全局平均池化 [B, C, H, W] -> [B, C, 1, 1]
        x = self.avg_pool(x)

        # Excitation: 学习通道权重
        x = self.fc1(x)
        x = self.activation(x)
        x = self.fc2(x)
        x = self.gate_activation(x)  # [B, C, 1, 1]

        # Scale: 重新校准特征图
        return input_tensor * x


class ECAModule(nn.Module):
    """
    Efficient Channel Attention模块

    ECA是SE的轻量级变体，使用1D卷积替代全连接层，
    减少参数量的同时保持性能。

    Args:
        channels: 输入通道数
        gamma: 自适应核大小参数 (default: 2)
        b: 自适应核大小参数 (default: 1)
    """

    def __init__(self, channels: int, gamma: int = 2, b: int = 1):
        super(ECAModule, self).__init__()

        # 自适应确定1D卷积核大小
        kernel_size = int(abs((torch.log2(torch.tensor(channels, dtype=torch.float)) + b) / gamma))
        kernel_size = kernel_size if kernel_size % 2 else kernel_size + 1

        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=kernel_size, padding=(kernel_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播

        Args:
            x: 输入特征图 [B, C, H, W]

        Returns:
            重新校准后的特征图 [B, C, H, W]
        """
        # 保存输入
        input_tensor = x

        # 全局平均池化
        y = self.avg_pool(x)  # [B, C, 1, 1]

        # 转换为1D卷积的输入格式
        y = y.squeeze(-1).transpose(-1, -2)  # [B, 1, C]

        # 1D卷积学习通道依赖
        y = self.conv(y)  # [B, 1, C]

        # 转换回原始格式
        y = y.transpose(-1, -2).unsqueeze(-1)  # [B, C, 1, 1]

        # Sigmoid激活
        y = self.sigmoid(y)

        # 重新校准
        return input_tensor * y


class CombinedAttention(nn.Module):
    """
    复合注意力模块 - 串联架构

    实现SE注意力 → EMA/SimAM注意力的顺序串联
    基于研究结论：通道建模 + 空间建模的互补性设计

    Args:
        channels: 输入通道数
        base_attention_type: 基础注意力类型 (默认: 'se')
        enhanced_attention_type: 增强注意力类型 ('ema' 或 'simam')
        base_attention_config: 基础注意力配置参数
        enhanced_attention_config: 增强注意力配置参数
    """

    def __init__(
        self,
        channels: int,
        base_attention_type: str = 'se',
        enhanced_attention_type: str = None,
        base_attention_config: Optional[Dict[str, Any]] = None,
        enhanced_attention_config: Optional[Dict[str, Any]] = None
    ):
        super(CombinedAttention, self).__init__()

        self.channels = channels
        self.base_attention_type = base_attention_type
        self.enhanced_attention_type = enhanced_attention_type

        # 创建基础注意力模块（SE）
        base_config = base_attention_config or {}
        self.base_attention = self._create_base_attention(
            base_attention_type, channels, **base_config
        )

        # 创建增强注意力模块（EMA/SimAM）
        self.enhanced_attention = None
        if enhanced_attention_type:
            enhanced_config = enhanced_attention_config or {}
            self.enhanced_attention = self._create_enhanced_attention(
                enhanced_attention_type, channels, **enhanced_config
            )

    def _create_base_attention(self, attention_type: str, channels: int, **kwargs) -> nn.Module:
        """创建基础注意力模块"""
        if attention_type.lower() == 'se':
            reduction = kwargs.get('reduction', 16)
            return SEModule(channels, reduction)
        elif attention_type.lower() == 'eca':
            gamma = kwargs.get('gamma', 2)
            b = kwargs.get('b', 1)
            return ECAModule(channels, gamma, b)
        elif attention_type.lower() == 'none':
            return nn.Identity()
        else:
            raise ValueError(f"Unsupported base attention type: {attention_type}")

    def _create_enhanced_attention(self, attention_type: str, channels: int, **kwargs) -> nn.Module:
        """创建增强注意力模块"""
        if attention_type.lower() == 'ema':
            factor = kwargs.get('factor', 8)
            return EMAModule(channels, factor)
        elif attention_type.lower() == 'simam':
            e_lambda = kwargs.get('e_lambda', 1e-4)
            return SimAMModule(e_lambda)
        else:
            raise ValueError(f"Unsupported enhanced attention type: {attention_type}")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播 - 串联执行

        执行顺序：输入 → 基础注意力(SE) → 增强注意力(EMA/SimAM) → 输出

        Args:
            x: 输入特征张量 [B, C, H, W]

        Returns:
            处理后的特征张量 [B, C, H, W]
        """
        # 第一阶段：基础注意力（通道建模）
        x = self.base_attention(x)

        # 第二阶段：增强注意力（空间建模）
        if self.enhanced_attention is not None:
            x = self.enhanced_attention(x)

        return x

    def get_attention_info(self) -> Dict[str, str]:
        """获取注意力模块信息"""
        info = {
            'base_attention': self.base_attention_type,
            'enhanced_attention': self.enhanced_attention_type or 'none',
            'architecture': 'serial_combination'
        }
        return info


def create_attention_module(
    attention_type: str,
    channels: int,
    **kwargs
) -> nn.Module:
    """
    注意力模块工厂函数

    Args:
        attention_type: 注意力类型 ('se', 'eca', 'none')
        channels: 输入通道数
        **kwargs: 其他参数

    Returns:
        注意力模块实例
    """
    if attention_type.lower() == 'se':
        reduction = kwargs.get('reduction', 16)
        return SEModule(channels, reduction)
    elif attention_type.lower() == 'eca':
        gamma = kwargs.get('gamma', 2)
        b = kwargs.get('b', 1)
        return ECAModule(channels, gamma, b)
    elif attention_type.lower() == 'none':
        return nn.Identity()
    else:
        raise ValueError(f"Unsupported attention type: {attention_type}")


# 导出的公共接口
__all__ = [
    'SEModule',
    'ECAModule',
    'EMAModule',
    'SimAMModule',
    'CombinedAttention',
    'create_attention_module'
]
