"""
SE注意力机制模块
实现Squeeze-and-Excitation注意力机制，增强特征表达能力

基于论文：Squeeze-and-Excitation Networks (CVPR 2018)
数学表述：
- Squeeze: z = GAP(F) = (1/HW) ∑ᵢ₌₁ᴴ ∑ⱼ₌₁ᵂ F(i,j)
- Excitation: s = σ(W₂ · ReLU(W₁ · z))
- Scale: F' = s ⊙ F
"""

from typing import Optional
import torch
import torch.nn as nn
from torch import Tensor


class SEModule(nn.Module):
    """
    Squeeze-and-Excitation模块
    
    通过全局平均池化和两个全连接层学习通道间的依赖关系，
    自适应地重新校准通道特征响应。
    
    Args:
        channels: 输入通道数
        reduction: 降维比例，控制中间层的通道数 (default: 16)
        activation: 激活函数类型 (default: ReLU)
        gate_activation: 门控激活函数 (default: Sigmoid)
    """
    
    def __init__(
        self,
        channels: int,
        reduction: int = 16,
        activation: nn.Module = nn.ReLU,
        gate_activation: nn.Module = nn.Sigmoid
    ):
        super(SEModule, self).__init__()
        
        if channels < reduction:
            raise ValueError(f"channels ({channels}) must be >= reduction ({reduction})")
        
        reduced_channels = max(channels // reduction, 1)
        
        # Squeeze: 全局平均池化
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        
        # Excitation: 两个全连接层
        self.fc1 = nn.Conv2d(channels, reduced_channels, kernel_size=1, padding=0, bias=True)
        self.activation = activation(inplace=True)
        self.fc2 = nn.Conv2d(reduced_channels, channels, kernel_size=1, padding=0, bias=True)
        self.gate_activation = gate_activation()
        
        # 权重初始化
        self._initialize_weights()
    
    def _initialize_weights(self):
        """权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征图 [B, C, H, W]
            
        Returns:
            重新校准后的特征图 [B, C, H, W]
        """
        # 保存输入用于残差连接
        input_tensor = x
        
        # Squeeze: 全局平均池化 [B, C, H, W] -> [B, C, 1, 1]
        x = self.avg_pool(x)
        
        # Excitation: 学习通道权重
        x = self.fc1(x)
        x = self.activation(x)
        x = self.fc2(x)
        x = self.gate_activation(x)  # [B, C, 1, 1]
        
        # Scale: 重新校准特征图
        return input_tensor * x


class ECAModule(nn.Module):
    """
    Efficient Channel Attention模块
    
    ECA是SE的轻量级变体，使用1D卷积替代全连接层，
    减少参数量的同时保持性能。
    
    Args:
        channels: 输入通道数
        gamma: 自适应核大小参数 (default: 2)
        b: 自适应核大小参数 (default: 1)
    """
    
    def __init__(self, channels: int, gamma: int = 2, b: int = 1):
        super(ECAModule, self).__init__()
        
        # 自适应确定1D卷积核大小
        kernel_size = int(abs((torch.log2(torch.tensor(channels, dtype=torch.float)) + b) / gamma))
        kernel_size = kernel_size if kernel_size % 2 else kernel_size + 1
        
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.conv = nn.Conv1d(1, 1, kernel_size=kernel_size, padding=(kernel_size - 1) // 2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征图 [B, C, H, W]
            
        Returns:
            重新校准后的特征图 [B, C, H, W]
        """
        # 保存输入
        input_tensor = x
        
        # 全局平均池化
        y = self.avg_pool(x)  # [B, C, 1, 1]
        
        # 转换为1D卷积的输入格式
        y = y.squeeze(-1).transpose(-1, -2)  # [B, 1, C]
        
        # 1D卷积学习通道依赖
        y = self.conv(y)  # [B, 1, C]
        
        # 转换回原始格式
        y = y.transpose(-1, -2).unsqueeze(-1)  # [B, C, 1, 1]
        
        # Sigmoid激活
        y = self.sigmoid(y)
        
        # 重新校准
        return input_tensor * y


def create_attention_module(
    attention_type: str,
    channels: int,
    **kwargs
) -> nn.Module:
    """
    注意力模块工厂函数
    
    Args:
        attention_type: 注意力类型 ('se', 'eca', 'none')
        channels: 输入通道数
        **kwargs: 其他参数
        
    Returns:
        注意力模块实例
    """
    if attention_type.lower() == 'se':
        reduction = kwargs.get('reduction', 16)
        return SEModule(channels, reduction)
    elif attention_type.lower() == 'eca':
        gamma = kwargs.get('gamma', 2)
        b = kwargs.get('b', 1)
        return ECAModule(channels, gamma, b)
    elif attention_type.lower() == 'none':
        return nn.Identity()
    else:
        raise ValueError(f"Unsupported attention type: {attention_type}")


# 导出的公共接口
__all__ = [
    'SEModule',
    'ECAModule', 
    'create_attention_module'
]
