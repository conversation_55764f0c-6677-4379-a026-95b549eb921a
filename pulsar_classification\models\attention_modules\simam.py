"""
SimAM (Simple, Parameter-Free Attention Module)
From: SimAM: A Simple, Parameter-Free Attention Module for Convolutional Neural Networks (ICML 2021)

Integrated for PulsarRes2Net architecture with mixed precision compatibility.
"""

import torch
import torch.nn as nn


class SimAMModule(nn.Module):
    """
    Simple, Parameter-Free Attention Module
    
    Args:
        e_lambda: Regularization parameter (default: 1e-4)
    """
    def __init__(self, e_lambda: float = 1e-4):
        super(SimAMModule, self).__init__()
        self.e_lambda = e_lambda
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        b, c, h, w = x.size()
        n = w * h - 1
        
        # Calculate spatial importance
        x_minus_mu_square = (x - x.mean(dim=[2, 3], keepdim=True)).pow(2)
        y = x_minus_mu_square / (4 * (x_minus_mu_square.sum(dim=[2, 3], keepdim=True) / n + self.e_lambda)) + 0.5
        
        return x * self.sigmoid(y)
