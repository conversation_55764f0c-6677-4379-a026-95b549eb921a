"""
混合通道适配器模块
实现理论公式：A(X) = α₁X + α₂Conv1×1(X) + α₃Edge(X)

基于脉冲星物理学的单通道到多通道适配策略，结合：
1. 原始信息保持 (α₁X)
2. 可学习线性变换 (α₂Conv1×1(X))  
3. 物理特征增强 (α₃Edge(X))

理论依据：
- 信息保持：I(X; A(X)) ≈ I(X; X)
- 判别性增强：max I(A(X); Y)
- 物理约束：保持脉冲星信号的物理特性
"""

from typing import Optional, Tuple, Dict, Any
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor


class PhysicsConstrainedChannelAdapter(nn.Module):
    """
    物理约束的混合通道适配器
    
    实现理论公式：A(X) = α₁X + α₂Conv1×1(X) + α₃Edge(X)
    
    Args:
        use_learnable: 是否使用可学习的1×1卷积适配
        use_edge_features: 是否使用边缘特征提取
        edge_kernel_type: 边缘检测核类型 ('sobel', 'laplacian', 'learnable')
        normalize_weights: 是否归一化融合权重
        init_weights: 初始融合权重 [α₁, α₂, α₃]
    """
    
    def __init__(
        self,
        use_learnable: bool = True,
        use_edge_features: bool = True,
        edge_kernel_type: str = 'sobel',
        normalize_weights: bool = True,
        init_weights: Optional[Tuple[float, float, float]] = None
    ):
        super(PhysicsConstrainedChannelAdapter, self).__init__()

        self.use_learnable = use_learnable
        self.use_edge_features = use_edge_features
        self.normalize_weights = normalize_weights
        self.edge_kernel_type = edge_kernel_type

        # 可学习的1×1卷积适配
        if use_learnable:
            self.learnable_conv = nn.Conv2d(1, 1, kernel_size=1, bias=False)
            self.learnable_bn = nn.BatchNorm2d(1)
            # 初始化为恒等变换
            nn.init.constant_(self.learnable_conv.weight, 1.0)

        # 边缘特征提取
        if use_edge_features:
            self._setup_edge_detection(edge_kernel_type)

        # 融合权重参数
        if init_weights is None:
            init_weights = (1.0, 1.0, 1.0)
        self.fusion_weights = nn.Parameter(torch.tensor(init_weights, dtype=torch.float32))

        # 最终的批归一化
        self.final_bn = nn.BatchNorm2d(3)

        # 数据类型追踪
        self._input_dtype = None
        
    def _setup_edge_detection(self, kernel_type: str):
        """设置边缘检测核"""
        if kernel_type == 'sobel':
            # Sobel算子 - 检测水平边缘（适合脉冲星相位变化）
            sobel_kernel = torch.tensor([
                [-1, 0, 1],
                [-2, 0, 2],
                [-1, 0, 1]
            ], dtype=torch.float32).view(1, 1, 3, 3)
            self.register_buffer('edge_kernel', sobel_kernel)

        elif kernel_type == 'laplacian':
            # Laplacian算子 - 检测所有方向边缘
            laplacian_kernel = torch.tensor([
                [0, -1, 0],
                [-1, 4, -1],
                [0, -1, 0]
            ], dtype=torch.float32).view(1, 1, 3, 3)
            self.register_buffer('edge_kernel', laplacian_kernel)

        elif kernel_type == 'learnable':
            # 可学习的边缘检测核
            self.edge_conv = nn.Conv2d(1, 1, kernel_size=3, padding=1, bias=False)
            # 初始化为Sobel核
            with torch.no_grad():
                sobel_init = torch.tensor([
                    [-1, 0, 1],
                    [-2, 0, 2],
                    [-1, 0, 1]
                ], dtype=torch.float32).view(1, 1, 3, 3)
                self.edge_conv.weight.copy_(sobel_init)
        else:
            raise ValueError(f"Unsupported edge kernel type: {kernel_type}")

    def _ensure_dtype_compatibility(self, x: Tensor):
        """确保所有组件与输入数据类型兼容"""
        target_dtype = x.dtype
        target_device = x.device

        # 只在数据类型改变时更新
        if self._input_dtype != target_dtype:
            self._input_dtype = target_dtype

            # 更新边缘检测核的数据类型
            if hasattr(self, 'edge_kernel'):
                self.edge_kernel = self.edge_kernel.to(dtype=target_dtype, device=target_device)

            # 更新可学习卷积层的数据类型
            if self.use_learnable:
                self.learnable_conv = self.learnable_conv.to(dtype=target_dtype, device=target_device)
                self.learnable_bn = self.learnable_bn.to(dtype=target_dtype, device=target_device)

            # 更新边缘卷积层的数据类型
            if hasattr(self, 'edge_conv'):
                self.edge_conv = self.edge_conv.to(dtype=target_dtype, device=target_device)

            # 更新最终批归一化的数据类型
            self.final_bn = self.final_bn.to(dtype=target_dtype, device=target_device)

            # 更新融合权重的数据类型
            self.fusion_weights.data = self.fusion_weights.data.to(dtype=target_dtype, device=target_device)
    
    def _extract_edge_features(self, x: Tensor) -> Tensor:
        """提取边缘特征"""
        if hasattr(self, 'edge_conv'):
            # 可学习边缘检测
            return self.edge_conv(x)
        else:
            # 固定核边缘检测
            # 确保edge_kernel与输入数据类型匹配
            edge_kernel = self.edge_kernel.to(dtype=x.dtype, device=x.device)
            return F.conv2d(x, edge_kernel, padding=1)
    
    def forward(self, x: Tensor) -> Tensor:
        """
        前向传播

        Args:
            x: 输入张量 [B, 1, H, W]

        Returns:
            输出张量 [B, 3, H, W]
        """
        if x.size(1) != 1:
            raise ValueError(f"Expected input with 1 channel, got {x.size(1)}")

        # 确保所有组件与输入数据类型兼容
        self._ensure_dtype_compatibility(x)

        channels = []

        # 原始通道 (α₁X)
        channels.append(x)

        # 可学习适配通道 (α₂Conv1×1(X))
        if self.use_learnable:
            learnable_channel = self.learnable_conv(x)
            learnable_channel = self.learnable_bn(learnable_channel)
            channels.append(learnable_channel)
        else:
            channels.append(x.clone())

        # 边缘特征通道 (α₃Edge(X))
        if self.use_edge_features:
            edge_channel = self._extract_edge_features(x)
            channels.append(edge_channel)
        else:
            channels.append(x.clone())

        # 加权融合
        if self.normalize_weights:
            # 确保fusion_weights与输入数据类型匹配
            fusion_weights = self.fusion_weights.to(dtype=x.dtype, device=x.device)
            weights = F.softmax(fusion_weights, dim=0)
        else:
            fusion_weights = self.fusion_weights.to(dtype=x.dtype, device=x.device)
            weights = torch.abs(fusion_weights)  # 确保权重为正

        # 逐通道加权
        weighted_channels = []
        for i, channel in enumerate(channels):
            # 确保权重与通道数据类型匹配
            weight = weights[i].to(dtype=channel.dtype, device=channel.device)
            weighted_channels.append(weight * channel)

        # 拼接通道
        output = torch.cat(weighted_channels, dim=1)

        # 最终批归一化
        output = self.final_bn(output)

        return output
    
    def get_fusion_weights(self) -> Tensor:
        """获取当前的融合权重"""
        if self.normalize_weights:
            # 数值稳定的softmax实现
            weights_shifted = self.fusion_weights - torch.max(self.fusion_weights)
            weights = F.softmax(weights_shifted, dim=0)

            # 确保权重不会过小或过大
            eps = 1e-6
            weights = torch.clamp(weights, min=eps, max=1.0-eps)

            # 重新归一化
            weights = weights / weights.sum()
            return weights
        else:
            return torch.abs(self.fusion_weights)

    def half(self):
        """转换为半精度"""
        super().half()
        # 更新数据类型追踪
        self._input_dtype = torch.float16
        return self

    def float(self):
        """转换为单精度"""
        super().float()
        # 更新数据类型追踪
        self._input_dtype = torch.float32
        return self

    def to(self, *args, **kwargs):
        """重写to方法以确保数据类型一致性"""
        result = super().to(*args, **kwargs)

        # 如果指定了dtype，更新追踪
        if 'dtype' in kwargs:
            result._input_dtype = kwargs['dtype']
        elif len(args) > 0 and hasattr(args[0], 'dtype'):
            result._input_dtype = args[0].dtype

        return result
    
    def get_weight_info(self) -> Dict[str, float]:
        """获取权重信息用于分析"""
        weights = self.get_fusion_weights()
        return {
            'original_weight': weights[0].item(),
            'learnable_weight': weights[1].item(),
            'edge_weight': weights[2].item()
        }


class SimpleChannelAdapter(nn.Module):
    """简单的通道适配器 - 用于消融实验对比"""
    
    def __init__(self, strategy: str = 'repeat'):
        super(SimpleChannelAdapter, self).__init__()
        self.strategy = strategy
        
        if strategy == 'learnable':
            self.conv = nn.Conv2d(1, 3, kernel_size=1, bias=False)
            self.bn = nn.BatchNorm2d(3)
            # 初始化为重复策略的等价形式
            with torch.no_grad():
                self.conv.weight.fill_(1.0)
    
    def forward(self, x: Tensor) -> Tensor:
        if self.strategy == 'repeat':
            return x.repeat(1, 3, 1, 1)
        elif self.strategy == 'learnable':
            return self.bn(self.conv(x))
        else:
            raise ValueError(f"Unsupported strategy: {self.strategy}")


def create_channel_adapter(config: Dict[str, Any]) -> nn.Module:
    """
    通道适配器工厂函数
    
    Args:
        config: 配置字典
        
    Returns:
        通道适配器实例
    """
    adapter_type = config.get('type', 'physics_constrained')
    
    if adapter_type == 'physics_constrained':
        return PhysicsConstrainedChannelAdapter(
            use_learnable=config.get('use_learnable', True),
            use_edge_features=config.get('use_edge_features', True),
            edge_kernel_type=config.get('edge_kernel_type', 'sobel'),
            normalize_weights=config.get('normalize_weights', True),
            init_weights=config.get('init_weights', None)
        )
    elif adapter_type == 'simple':
        return SimpleChannelAdapter(
            strategy=config.get('strategy', 'repeat')
        )
    else:
        raise ValueError(f"Unsupported adapter type: {adapter_type}")


# 导出的公共接口
__all__ = [
    'PhysicsConstrainedChannelAdapter',
    'SimpleChannelAdapter',
    'create_channel_adapter'
]
