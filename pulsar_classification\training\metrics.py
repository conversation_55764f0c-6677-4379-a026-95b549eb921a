"""
评估指标模块
实现脉冲星分类任务的专门化评估指标

关键指标：
1. 准确率 (Accuracy)
2. 精确率 (Precision) 
3. 召回率 (Recall)
4. F1分数 (F1-Score)
5. 假阳性率 (FPR) - 脉冲星搜索的关键指标
6. 假阴性率 (FNR) - 脉冲星搜索的关键指标
7. AUC-ROC
8. 混淆矩阵
"""

from typing import Dict, List, Optional, Tuple, Union
import torch
import torch.nn.functional as F
from torch import Tensor
import numpy as np
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_auc_score, confusion_matrix, classification_report
)


class MetricsCalculator:
    """
    指标计算器
    累积预测结果并计算各种评估指标
    """
    
    def __init__(self, num_classes: int = 2):
        self.num_classes = num_classes
        self.reset()
    
    def reset(self):
        """重置累积的预测结果"""
        self.predictions = []
        self.targets = []
        self.probabilities = []
    
    def update(self, pred: Tensor, target: Tensor, prob: Optional[Tensor] = None):
        """
        更新预测结果
        
        Args:
            pred: 预测类别 [B] 或 logits [B, C]
            target: 真实标签 [B]
            prob: 预测概率 [B, C] (可选)
        """
        # 处理logits输入
        if pred.dim() > 1:
            prob = F.softmax(pred, dim=1)
            pred = torch.argmax(pred, dim=1)
        
        # 转换为numpy数组
        pred_np = pred.detach().cpu().numpy()
        target_np = target.detach().cpu().numpy()
        
        self.predictions.extend(pred_np.tolist())
        self.targets.extend(target_np.tolist())
        
        if prob is not None:
            prob_np = prob.detach().cpu().numpy()
            self.probabilities.extend(prob_np.tolist())
    
    def compute(self) -> Dict[str, float]:
        """
        计算所有评估指标
        
        Returns:
            指标字典
        """
        if len(self.predictions) == 0:
            return {}
        
        y_true = np.array(self.targets)
        y_pred = np.array(self.predictions)
        
        metrics = {}
        
        # 基础分类指标
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
        
        # 二分类特定指标
        if self.num_classes == 2:
            # 混淆矩阵
            tn, fp, fn, tp = confusion_matrix(y_true, y_pred).ravel()
            
            # 脉冲星搜索关键指标
            metrics['true_positive_rate'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            metrics['false_positive_rate'] = fp / (fp + tn) if (fp + tn) > 0 else 0.0
            metrics['false_negative_rate'] = fn / (fn + tp) if (fn + tp) > 0 else 0.0
            metrics['true_negative_rate'] = tn / (tn + fp) if (tn + fp) > 0 else 0.0
            
            # 精确率和召回率（针对脉冲星类别）
            metrics['pulsar_precision'] = tp / (tp + fp) if (tp + fp) > 0 else 0.0
            metrics['pulsar_recall'] = tp / (tp + fn) if (tp + fn) > 0 else 0.0
            metrics['pulsar_f1'] = 2 * metrics['pulsar_precision'] * metrics['pulsar_recall'] / \
                                  (metrics['pulsar_precision'] + metrics['pulsar_recall']) \
                                  if (metrics['pulsar_precision'] + metrics['pulsar_recall']) > 0 else 0.0
            
            # AUC-ROC（如果有概率预测）
            if len(self.probabilities) > 0:
                y_prob = np.array(self.probabilities)
                if y_prob.shape[1] >= 2:
                    metrics['auc_roc'] = roc_auc_score(y_true, y_prob[:, 1])
        
        return metrics
    
    def get_confusion_matrix(self) -> np.ndarray:
        """获取混淆矩阵"""
        if len(self.predictions) == 0:
            return np.zeros((self.num_classes, self.num_classes))
        
        y_true = np.array(self.targets)
        y_pred = np.array(self.predictions)
        
        return confusion_matrix(y_true, y_pred)
    
    def get_classification_report(self) -> str:
        """获取分类报告"""
        if len(self.predictions) == 0:
            return "No predictions available"
        
        y_true = np.array(self.targets)
        y_pred = np.array(self.predictions)
        
        target_names = ['non_pulsar', 'pulsar'] if self.num_classes == 2 else None
        
        return classification_report(y_true, y_pred, target_names=target_names)


class PulsarMetrics:
    """
    脉冲星特定的评估指标
    专门针对脉冲星搜索任务的性能评估
    """
    
    @staticmethod
    def detection_efficiency(tp: int, fn: int) -> float:
        """
        检测效率 = TP / (TP + FN)
        衡量模型发现真实脉冲星的能力
        """
        return tp / (tp + fn) if (tp + fn) > 0 else 0.0
    
    @staticmethod
    def contamination_rate(fp: int, tp: int) -> float:
        """
        污染率 = FP / (FP + TP)
        衡量假阳性在所有阳性预测中的比例
        """
        return fp / (fp + tp) if (fp + tp) > 0 else 0.0
    
    @staticmethod
    def discovery_potential(tp: int, fp: int, total_candidates: int) -> float:
        """
        发现潜力 = TP / total_candidates
        衡量在所有候选中发现真实脉冲星的比例
        """
        return tp / total_candidates if total_candidates > 0 else 0.0
    
    @staticmethod
    def efficiency_purity_product(tp: int, fp: int, fn: int) -> float:
        """
        效率-纯度乘积 = (TP/(TP+FN)) × (TP/(TP+FP))
        综合衡量检测效率和纯度的指标
        """
        efficiency = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        purity = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        return efficiency * purity


def calculate_metrics_from_confusion_matrix(cm: np.ndarray) -> Dict[str, float]:
    """
    从混淆矩阵计算指标
    
    Args:
        cm: 混淆矩阵
        
    Returns:
        指标字典
    """
    if cm.shape != (2, 2):
        raise ValueError("Only binary classification confusion matrix supported")
    
    tn, fp, fn, tp = cm.ravel()
    
    metrics = {
        'true_positives': int(tp),
        'false_positives': int(fp),
        'true_negatives': int(tn),
        'false_negatives': int(fn),
        'accuracy': (tp + tn) / (tp + tn + fp + fn) if (tp + tn + fp + fn) > 0 else 0.0,
        'precision': tp / (tp + fp) if (tp + fp) > 0 else 0.0,
        'recall': tp / (tp + fn) if (tp + fn) > 0 else 0.0,
        'specificity': tn / (tn + fp) if (tn + fp) > 0 else 0.0,
        'false_positive_rate': fp / (fp + tn) if (fp + tn) > 0 else 0.0,
        'false_negative_rate': fn / (fn + tp) if (fn + tp) > 0 else 0.0
    }
    
    # F1分数
    if metrics['precision'] + metrics['recall'] > 0:
        metrics['f1_score'] = 2 * metrics['precision'] * metrics['recall'] / \
                             (metrics['precision'] + metrics['recall'])
    else:
        metrics['f1_score'] = 0.0
    
    # 脉冲星特定指标
    metrics['detection_efficiency'] = PulsarMetrics.detection_efficiency(tp, fn)
    metrics['contamination_rate'] = PulsarMetrics.contamination_rate(fp, tp)
    metrics['efficiency_purity_product'] = PulsarMetrics.efficiency_purity_product(tp, fp, fn)
    
    return metrics


class AverageMeter:
    """平均值计算器"""
    
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0
    
    def update(self, val: float, n: int = 1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count


def accuracy(pred: Tensor, target: Tensor, topk: Tuple[int, ...] = (1,)) -> List[float]:
    """
    计算top-k准确率
    
    Args:
        pred: 预测logits [B, C]
        target: 真实标签 [B]
        topk: top-k值的元组
        
    Returns:
        top-k准确率列表
    """
    with torch.no_grad():
        maxk = max(topk)
        batch_size = target.size(0)
        
        _, pred_indices = pred.topk(maxk, 1, True, True)
        pred_indices = pred_indices.t()
        correct = pred_indices.eq(target.view(1, -1).expand_as(pred_indices))
        
        res = []
        for k in topk:
            correct_k = correct[:k].reshape(-1).float().sum(0, keepdim=True)
            res.append(correct_k.mul_(100.0 / batch_size).item())
        
        return res


# 导出的公共接口
__all__ = [
    'MetricsCalculator',
    'PulsarMetrics',
    'calculate_metrics_from_confusion_matrix',
    'AverageMeter',
    'accuracy'
]
