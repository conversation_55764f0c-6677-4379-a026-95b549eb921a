"""
可重现性工具模块
确保实验结果的可重现性

特性：
- 随机种子设置
- 确定性算法配置
- 环境信息记录
- 依赖版本检查
"""

import os
import random
import numpy as np
import torch
import logging
from typing import Dict, Any, Optional
import platform
import sys


def set_random_seed(seed: int = 42, deterministic: bool = True):
    """
    设置随机种子以确保可重现性
    
    Args:
        seed: 随机种子
        deterministic: 是否使用确定性算法
    """
    # Python随机种子
    random.seed(seed)
    
    # NumPy随机种子
    np.random.seed(seed)
    
    # PyTorch随机种子
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    
    # 确定性算法设置
    if deterministic:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
        
        # 设置环境变量以确保确定性
        os.environ['PYTHONHASHSEED'] = str(seed)
        os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'
        
        # PyTorch 1.8+的确定性设置
        if hasattr(torch, 'use_deterministic_algorithms'):
            torch.use_deterministic_algorithms(True)
    else:
        # 性能优化设置
        torch.backends.cudnn.benchmark = True


def get_environment_info() -> Dict[str, Any]:
    """
    获取环境信息
    
    Returns:
        环境信息字典
    """
    env_info = {
        'platform': {
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
        },
        'python': {
            'version': sys.version,
            'executable': sys.executable,
            'path': sys.path[:3]  # 只记录前3个路径
        },
        'pytorch': {
            'version': torch.__version__,
            'cuda_available': torch.cuda.is_available(),
            'cuda_version': torch.version.cuda if torch.cuda.is_available() else None,
            'cudnn_version': torch.backends.cudnn.version() if torch.cuda.is_available() else None,
            'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
        }
    }
    
    # GPU信息
    if torch.cuda.is_available():
        gpu_info = []
        for i in range(torch.cuda.device_count()):
            gpu_props = torch.cuda.get_device_properties(i)
            gpu_info.append({
                'name': gpu_props.name,
                'memory_total': gpu_props.total_memory,
                'memory_free': torch.cuda.get_device_properties(i).total_memory - torch.cuda.memory_allocated(i),
                'compute_capability': f"{gpu_props.major}.{gpu_props.minor}"
            })
        env_info['gpu'] = gpu_info
    
    # 尝试获取其他重要包的版本
    try:
        import numpy
        env_info['numpy_version'] = numpy.__version__
    except ImportError:
        pass
    
    try:
        import sklearn
        env_info['sklearn_version'] = sklearn.__version__
    except ImportError:
        pass
    
    try:
        import PIL
        env_info['pillow_version'] = PIL.__version__
    except ImportError:
        pass
    
    return env_info


def log_environment_info(logger: logging.Logger):
    """
    记录环境信息到日志
    
    Args:
        logger: 日志记录器
    """
    env_info = get_environment_info()
    
    logger.info("Environment Information:")
    logger.info(f"  Platform: {env_info['platform']['system']} {env_info['platform']['release']}")
    logger.info(f"  Python: {env_info['platform']['python_version']}")
    logger.info(f"  PyTorch: {env_info['pytorch']['version']}")
    
    if env_info['pytorch']['cuda_available']:
        logger.info(f"  CUDA: {env_info['pytorch']['cuda_version']}")
        logger.info(f"  cuDNN: {env_info['pytorch']['cudnn_version']}")
        logger.info(f"  GPU Count: {env_info['pytorch']['device_count']}")
        
        if 'gpu' in env_info:
            for i, gpu in enumerate(env_info['gpu']):
                logger.info(f"    GPU {i}: {gpu['name']} ({gpu['memory_total'] // (1024**3)}GB)")
    else:
        logger.info("  CUDA: Not available")
    
    # 记录其他包版本
    for key in ['numpy_version', 'sklearn_version', 'pillow_version']:
        if key in env_info:
            package_name = key.replace('_version', '').title()
            logger.info(f"  {package_name}: {env_info[key]}")


def check_dependencies() -> Dict[str, bool]:
    """
    检查依赖包是否正确安装
    
    Returns:
        依赖检查结果
    """
    dependencies = {
        'torch': False,
        'torchvision': False,
        'numpy': False,
        'pillow': False,
        'sklearn': False,
        'yaml': False
    }
    
    # 检查PyTorch
    try:
        import torch
        dependencies['torch'] = True
    except ImportError:
        pass
    
    # 检查torchvision
    try:
        import torchvision
        dependencies['torchvision'] = True
    except ImportError:
        pass
    
    # 检查NumPy
    try:
        import numpy
        dependencies['numpy'] = True
    except ImportError:
        pass
    
    # 检查Pillow
    try:
        import PIL
        dependencies['pillow'] = True
    except ImportError:
        pass
    
    # 检查scikit-learn
    try:
        import sklearn
        dependencies['sklearn'] = True
    except ImportError:
        pass
    
    # 检查PyYAML
    try:
        import yaml
        dependencies['yaml'] = True
    except ImportError:
        pass
    
    return dependencies


def save_experiment_metadata(
    save_path: str,
    config: Dict[str, Any],
    model_info: Optional[Dict[str, Any]] = None,
    seed: Optional[int] = None
):
    """
    保存实验元数据
    
    Args:
        save_path: 保存路径
        config: 实验配置
        model_info: 模型信息
        seed: 随机种子
    """
    import json
    from datetime import datetime
    
    metadata = {
        'timestamp': datetime.now().isoformat(),
        'config': config,
        'environment': get_environment_info(),
        'dependencies': check_dependencies()
    }
    
    if model_info:
        metadata['model_info'] = model_info
    
    if seed is not None:
        metadata['random_seed'] = seed
    
    # 确保目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # 保存元数据
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, indent=2, default=str)


def load_experiment_metadata(metadata_path: str) -> Dict[str, Any]:
    """
    加载实验元数据
    
    Args:
        metadata_path: 元数据文件路径
        
    Returns:
        实验元数据
    """
    import json
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)
    
    return metadata


def verify_reproducibility(
    metadata_path: str,
    current_config: Dict[str, Any],
    tolerance: float = 1e-6
) -> Dict[str, bool]:
    """
    验证实验的可重现性
    
    Args:
        metadata_path: 原始实验元数据路径
        current_config: 当前实验配置
        tolerance: 数值比较容差
        
    Returns:
        验证结果
    """
    try:
        original_metadata = load_experiment_metadata(metadata_path)
        current_env = get_environment_info()
        
        verification = {
            'config_match': True,
            'environment_match': True,
            'dependencies_match': True,
            'pytorch_version_match': True,
            'cuda_version_match': True
        }
        
        # 检查配置匹配
        original_config = original_metadata.get('config', {})
        if original_config != current_config:
            verification['config_match'] = False
        
        # 检查PyTorch版本
        original_pytorch = original_metadata.get('environment', {}).get('pytorch', {}).get('version')
        current_pytorch = current_env['pytorch']['version']
        if original_pytorch != current_pytorch:
            verification['pytorch_version_match'] = False
        
        # 检查CUDA版本
        original_cuda = original_metadata.get('environment', {}).get('pytorch', {}).get('cuda_version')
        current_cuda = current_env['pytorch']['cuda_version']
        if original_cuda != current_cuda:
            verification['cuda_version_match'] = False
        
        return verification
        
    except Exception as e:
        return {'error': str(e)}


class ReproducibilityManager:
    """可重现性管理器"""
    
    def __init__(self, experiment_name: str, save_dir: str = "experiments"):
        self.experiment_name = experiment_name
        self.save_dir = save_dir
        self.metadata_path = os.path.join(save_dir, experiment_name, "metadata.json")
        
        # 创建目录
        os.makedirs(os.path.dirname(self.metadata_path), exist_ok=True)
    
    def setup_reproducibility(
        self,
        seed: int = 42,
        deterministic: bool = True,
        config: Optional[Dict[str, Any]] = None
    ):
        """设置可重现性环境"""
        # 设置随机种子
        set_random_seed(seed, deterministic)
        
        # 保存元数据
        if config:
            save_experiment_metadata(
                self.metadata_path,
                config,
                seed=seed
            )
    
    def verify_setup(self, config: Dict[str, Any]) -> bool:
        """验证设置是否正确"""
        if os.path.exists(self.metadata_path):
            verification = verify_reproducibility(self.metadata_path, config)
            return all(verification.values())
        return True


# 导出的公共接口
__all__ = [
    'set_random_seed',
    'get_environment_info',
    'log_environment_info',
    'check_dependencies',
    'save_experiment_metadata',
    'load_experiment_metadata',
    'verify_reproducibility',
    'ReproducibilityManager'
]
