"""
损失函数模块
实现多目标损失函数，包括标签平滑和物理约束

损失函数组成：
1. 标签平滑交叉熵：提高泛化能力
2. 物理约束损失：保持物理特性
3. 正则化损失：防止过拟合

数学表述：
L_total = L_smooth + λ₁L_physics + λ₂L_reg
"""

from typing import Optional, Dict, Any
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch import Tensor


class LabelSmoothingCrossEntropy(nn.Module):
    """
    标签平滑交叉熵损失
    
    数学表述：
    L_smooth = -∑ᵢ ỹᵢ log(ŷᵢ)
    其中 ỹᵢ = (1-ε)yᵢ + ε/K
    
    Args:
        smoothing: 平滑系数 ε (default: 0.1)
        reduction: 损失归约方式 ('mean', 'sum', 'none')
    """
    
    def __init__(self, smoothing: float = 0.1, reduction: str = 'mean'):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.smoothing = smoothing
        self.reduction = reduction
    
    def forward(self, pred: Tensor, target: Tensor) -> Tensor:
        """
        前向传播
        
        Args:
            pred: 预测logits [B, C]
            target: 真实标签 [B]
            
        Returns:
            损失值
        """
        num_classes = pred.size(-1)
        
        # 转换为one-hot编码
        true_dist = torch.zeros_like(pred)
        true_dist.fill_(self.smoothing / (num_classes - 1))
        true_dist.scatter_(1, target.unsqueeze(1), 1.0 - self.smoothing)
        
        # 计算交叉熵
        log_pred = F.log_softmax(pred, dim=-1)
        loss = -torch.sum(true_dist * log_pred, dim=-1)
        
        if self.reduction == 'mean':
            return loss.mean()
        elif self.reduction == 'sum':
            return loss.sum()
        else:
            return loss


class FocalLoss(nn.Module):
    """
    Focal Loss用于处理类别不平衡
    
    数学表述：
    FL(p_t) = -α_t(1-p_t)^γ log(p_t)
    
    Args:
        alpha: 类别权重
        gamma: 聚焦参数 (default: 2.0)
        reduction: 损失归约方式
    """
    
    def __init__(self, alpha: Optional[Tensor] = None, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, pred: Tensor, target: Tensor) -> Tensor:
        """
        前向传播
        
        Args:
            pred: 预测logits [B, C]
            target: 真实标签 [B]
            
        Returns:
            损失值
        """
        ce_loss = F.cross_entropy(pred, target, reduction='none')
        pt = torch.exp(-ce_loss)
        
        # 计算alpha权重
        if self.alpha is not None:
            if self.alpha.device != target.device:
                self.alpha = self.alpha.to(target.device)
            at = self.alpha.gather(0, target)
            focal_loss = at * (1 - pt) ** self.gamma * ce_loss
        else:
            focal_loss = (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class PhysicsConstraintLoss(nn.Module):
    """
    物理约束损失
    确保模型输出符合脉冲星物理特性
    
    约束类型：
    1. 梯度一致性：保持边缘特征的物理意义
    2. 能量守恒：总能量不应剧烈变化
    3. 相位连续性：相位变化应该连续
    """
    
    def __init__(self, gradient_weight: float = 1.0, energy_weight: float = 0.1):
        super(PhysicsConstraintLoss, self).__init__()
        self.gradient_weight = gradient_weight
        self.energy_weight = energy_weight
        
        # Sobel算子用于梯度计算
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)
        
        self.register_buffer('sobel_x', sobel_x.view(1, 1, 3, 3))
        self.register_buffer('sobel_y', sobel_y.view(1, 1, 3, 3))
    
    def forward(self, original: Tensor, adapted: Tensor) -> Tensor:
        """
        计算物理约束损失
        
        Args:
            original: 原始输入 [B, 1, H, W]
            adapted: 适配后的输入 [B, 3, H, W]
            
        Returns:
            物理约束损失
        """
        # 提取原始通道（假设第一个通道是原始信号）
        adapted_original = adapted[:, 0:1, :, :]
        
        # 1. 梯度一致性损失
        grad_loss = self._gradient_consistency_loss(original, adapted_original)
        
        # 2. 能量守恒损失
        energy_loss = self._energy_conservation_loss(original, adapted_original)
        
        total_loss = self.gradient_weight * grad_loss + self.energy_weight * energy_loss
        
        return total_loss
    
    def _gradient_consistency_loss(self, original: Tensor, adapted: Tensor) -> Tensor:
        """梯度一致性损失"""
        # 计算原始图像的梯度
        grad_x_orig = F.conv2d(original, self.sobel_x, padding=1)
        grad_y_orig = F.conv2d(original, self.sobel_y, padding=1)

        # 计算适配图像的梯度
        grad_x_adapt = F.conv2d(adapted, self.sobel_x, padding=1)
        grad_y_adapt = F.conv2d(adapted, self.sobel_y, padding=1)

        # 数值稳定性：添加小的正则化项
        eps = 1e-6
        grad_x_orig = grad_x_orig + eps * torch.randn_like(grad_x_orig)
        grad_y_orig = grad_y_orig + eps * torch.randn_like(grad_y_orig)

        # 计算梯度差异（使用Huber损失提高鲁棒性）
        grad_diff_x = F.smooth_l1_loss(grad_x_adapt, grad_x_orig)
        grad_diff_y = F.smooth_l1_loss(grad_y_adapt, grad_y_orig)

        return grad_diff_x + grad_diff_y
    
    def _energy_conservation_loss(self, original: Tensor, adapted: Tensor) -> Tensor:
        """能量守恒损失"""
        # 计算总能量
        energy_orig = torch.sum(original ** 2, dim=(2, 3))
        energy_adapt = torch.sum(adapted ** 2, dim=(2, 3))

        # 数值稳定性保护
        eps = 1e-6  # 增大epsilon值提高数值稳定性
        energy_orig_safe = torch.clamp(energy_orig, min=eps)

        # 计算能量比例
        energy_ratio = energy_adapt / energy_orig_safe

        # 添加数值范围保护
        energy_ratio = torch.clamp(energy_ratio, min=0.1, max=10.0)

        # 期望能量比例接近1
        energy_loss = F.mse_loss(energy_ratio, torch.ones_like(energy_ratio))

        return energy_loss


class CombinedLoss(nn.Module):
    """
    组合损失函数
    整合分类损失、物理约束损失和正则化损失
    
    L_total = L_cls + λ₁L_physics + λ₂L_reg
    """
    
    def __init__(
        self,
        classification_loss: str = 'label_smoothing',
        label_smoothing: float = 0.1,
        physics_weight: float = 0.01,
        focal_alpha: Optional[Tensor] = None,
        focal_gamma: float = 2.0
    ):
        super(CombinedLoss, self).__init__()
        
        # 分类损失
        if classification_loss == 'label_smoothing':
            self.cls_loss = LabelSmoothingCrossEntropy(smoothing=label_smoothing)
        elif classification_loss == 'focal':
            self.cls_loss = FocalLoss(alpha=focal_alpha, gamma=focal_gamma)
        elif classification_loss == 'cross_entropy':
            self.cls_loss = nn.CrossEntropyLoss()
        else:
            raise ValueError(f"Unsupported classification loss: {classification_loss}")
        
        # 物理约束损失
        self.physics_loss = PhysicsConstraintLoss()
        self.physics_weight = physics_weight
    
    def forward(
        self,
        pred: Tensor,
        target: Tensor,
        original_input: Optional[Tensor] = None,
        adapted_input: Optional[Tensor] = None
    ) -> Dict[str, Tensor]:
        """
        前向传播
        
        Args:
            pred: 预测logits [B, C]
            target: 真实标签 [B]
            original_input: 原始输入 [B, 1, H, W]
            adapted_input: 适配后输入 [B, 3, H, W]
            
        Returns:
            损失字典
        """
        # 分类损失
        cls_loss = self.cls_loss(pred, target)
        
        # 物理约束损失
        physics_loss = torch.tensor(0.0, device=pred.device)
        if original_input is not None and adapted_input is not None:
            physics_loss = self.physics_loss(original_input, adapted_input)
        
        # 总损失
        total_loss = cls_loss + self.physics_weight * physics_loss
        
        return {
            'total_loss': total_loss,
            'classification_loss': cls_loss,
            'physics_loss': physics_loss
        }


def create_loss_function(config: Dict[str, Any]) -> nn.Module:
    """
    损失函数工厂函数
    
    Args:
        config: 损失函数配置
        
    Returns:
        损失函数实例
    """
    loss_type = config.get('type', 'label_smoothing_cross_entropy')
    
    if loss_type == 'label_smoothing_cross_entropy':
        return LabelSmoothingCrossEntropy(
            smoothing=config.get('label_smoothing', 0.1)
        )
    elif loss_type == 'focal':
        alpha = config.get('focal_alpha', None)
        if alpha is not None:
            alpha = torch.tensor(alpha)
        return FocalLoss(
            alpha=alpha,
            gamma=config.get('focal_gamma', 2.0)
        )
    elif loss_type == 'combined':
        return CombinedLoss(
            classification_loss=config.get('classification_loss', 'label_smoothing'),
            label_smoothing=config.get('label_smoothing', 0.1),
            physics_weight=config.get('physics_constraint_weight', 0.01),
            focal_alpha=config.get('focal_alpha', None),
            focal_gamma=config.get('focal_gamma', 2.0)
        )
    else:
        raise ValueError(f"Unsupported loss type: {loss_type}")


# 导出的公共接口
__all__ = [
    'LabelSmoothingCrossEntropy',
    'FocalLoss',
    'PhysicsConstraintLoss',
    'CombinedLoss',
    'create_loss_function'
]
