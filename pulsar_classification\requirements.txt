# 脉冲星分类Baseline模型依赖包
# 基于物理约束的Res2Net架构

# 核心深度学习框架
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# 数据处理和科学计算
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0
scikit-learn>=1.0.0

# 图像处理
Pillow>=8.3.0
opencv-python>=4.5.0

# 配置文件处理
PyYAML>=6.0

# 日志和可视化
tensorboard>=2.8.0
matplotlib>=3.5.0
seaborn>=0.11.0

# 进度条和工具
tqdm>=4.62.0
click>=8.0.0

# 数据验证和类型检查
pydantic>=1.8.0

# 可选：加速训练
# nvidia-ml-py3>=7.352.0  # GPU监控
# apex  # NVIDIA Apex for mixed precision (需要手动安装)

# 开发和测试工具
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.0.0
flake8>=3.9.0
isort>=5.9.0

# Jupyter支持（可选）
jupyter>=1.0.0
ipywidgets>=7.6.0

# 其他工具
psutil>=5.8.0  # 系统监控
h5py>=3.1.0    # HDF5文件支持（如果需要）
