"""
测试修复后的训练脚本

验证修复后的train_baseline.py是否能正确处理enhanced_attention配置
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from configs.base_config import load_config
from experiments.train_baseline import create_model


def test_fpp_ema_model_creation():
    """测试FPP+EMA模型创建"""
    print("=== 测试FPP+EMA模型创建 ===")
    
    # 创建模拟配置
    class MockConfig:
        def __init__(self):
            self.model = MockModelConfig()
    
    class MockModelConfig:
        def __init__(self):
            self.layers = [2, 3, 4, 2]
            self.num_classes = 2
            self.width = 64
            self.scales = 3
            self.attention_type = 'se'
            self.dropout_rate = 0.2
            self.zero_init_residual = False
            self.groups = 1
            self.channel_adapter = {
                'type': 'physics_constrained',
                'use_learnable': True,
                'use_edge_features': True,
                'edge_kernel_type': 'sobel',
                'normalize_weights': True,
                'init_weights': [1.0, 1.0, 1.0]
            }
            self.enhanced_attention = {
                'enabled': True,
                'type': 'ema',
                'ema_config': {'factor': 8},
                'simam_config': {'e_lambda': 1e-4}
            }
    
    config = MockConfig()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        model, model_info = create_model(config, device)
        
        expected_params = 46257230  # FPP+EMA预期参数数量
        actual_params = model_info['total_parameters']
        
        if actual_params == expected_params:
            print(f"✅ FPP+EMA模型创建成功: {actual_params:,} 参数")
            return True
        else:
            print(f"❌ FPP+EMA参数数量不匹配: 预期 {expected_params:,}, 实际 {actual_params:,}")
            return False
            
    except Exception as e:
        print(f"❌ FPP+EMA模型创建失败: {e}")
        return False


def test_tpp_simam_model_creation():
    """测试TPP+SimAM模型创建"""
    print("\n=== 测试TPP+SimAM模型创建 ===")
    
    # 创建模拟配置
    class MockConfig:
        def __init__(self):
            self.model = MockModelConfig()
    
    class MockModelConfig:
        def __init__(self):
            self.layers = [2, 3, 4, 2]
            self.num_classes = 2
            self.width = 64
            self.scales = 3
            self.attention_type = 'se'
            self.dropout_rate = 0.2
            self.zero_init_residual = False
            self.groups = 1
            self.channel_adapter = {
                'type': 'physics_constrained',
                'use_learnable': True,
                'use_edge_features': True,
                'edge_kernel_type': 'sobel',
                'normalize_weights': True,
                'init_weights': [1.0, 1.0, 1.0]
            }
            self.enhanced_attention = {
                'enabled': True,
                'type': 'simam',
                'ema_config': {'factor': 8},
                'simam_config': {'e_lambda': 1e-4}
            }
    
    config = MockConfig()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        model, model_info = create_model(config, device)
        
        expected_params = 39059534  # TPP+SimAM预期参数数量
        actual_params = model_info['total_parameters']
        
        if actual_params == expected_params:
            print(f"✅ TPP+SimAM模型创建成功: {actual_params:,} 参数")
            return True
        else:
            print(f"❌ TPP+SimAM参数数量不匹配: 预期 {expected_params:,}, 实际 {actual_params:,}")
            return False
            
    except Exception as e:
        print(f"❌ TPP+SimAM模型创建失败: {e}")
        return False


def test_baseline_model_creation():
    """测试baseline模型创建"""
    print("\n=== 测试baseline模型创建 ===")
    
    # 创建模拟配置
    class MockConfig:
        def __init__(self):
            self.model = MockModelConfig()
    
    class MockModelConfig:
        def __init__(self):
            self.layers = [2, 3, 4, 2]
            self.num_classes = 2
            self.width = 64
            self.scales = 3
            self.attention_type = 'se'
            self.dropout_rate = 0.2
            self.zero_init_residual = False
            self.groups = 1
            self.channel_adapter = {
                'type': 'physics_constrained',
                'use_learnable': True,
                'use_edge_features': True,
                'edge_kernel_type': 'sobel',
                'normalize_weights': True,
                'init_weights': [1.0, 1.0, 1.0]
            }
            self.enhanced_attention = {
                'enabled': False,
                'type': None,
                'ema_config': {'factor': 8},
                'simam_config': {'e_lambda': 1e-4}
            }
    
    config = MockConfig()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        model, model_info = create_model(config, device)
        
        expected_params = 39059534  # baseline预期参数数量
        actual_params = model_info['total_parameters']
        
        if actual_params == expected_params:
            print(f"✅ Baseline模型创建成功: {actual_params:,} 参数")
            return True
        else:
            print(f"❌ Baseline参数数量不匹配: 预期 {expected_params:,}, 实际 {actual_params:,}")
            return False
            
    except Exception as e:
        print(f"❌ Baseline模型创建失败: {e}")
        return False


def main():
    """主函数"""
    print("修复后训练脚本测试")
    print("=" * 50)
    
    # 测试三种配置
    results = []
    results.append(test_fpp_ema_model_creation())
    results.append(test_tpp_simam_model_creation())
    results.append(test_baseline_model_creation())
    
    # 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    if all(results):
        print("✅ 所有测试通过！修复后的训练脚本工作正常。")
        print("✅ enhanced_attention配置已正确传递到模型。")
        print("✅ 串联注意力架构已正确实施。")
    else:
        print("❌ 部分测试失败，需要进一步检查。")
    
    return all(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
