"""
PyTorch版本兼容性处理模块
解决不同PyTorch版本间的API差异问题，特别是混合精度训练相关的API

主要功能：
- 检测PyTorch版本并选择正确的API
- 提供统一的autocast和GradScaler接口
- 处理PyTorch 2.1.2中的API兼容性问题
- 支持CUDA和CPU设备的混合精度训练
"""

import torch
import logging
from typing import Tuple, Any, Optional, Dict
import warnings

logger = logging.getLogger(__name__)

def get_pytorch_version():
    """
    获取PyTorch版本信息
    
    Returns:
        str: PyTorch版本字符串
    """
    return torch.__version__

def parse_version(version_str: str) -> Tuple[int, int, int]:
    """
    解析版本字符串为数字元组
    
    Args:
        version_str: 版本字符串，如 "2.1.2"
        
    Returns:
        Tuple[int, int, int]: (major, minor, patch)
    """
    try:
        # 移除可能的后缀，如 "+cu118"
        clean_version = version_str.split('+')[0]
        parts = clean_version.split('.')
        major = int(parts[0])
        minor = int(parts[1]) if len(parts) > 1 else 0
        patch = int(parts[2]) if len(parts) > 2 else 0
        return (major, minor, patch)
    except (ValueError, IndexError) as e:
        logger.warning(f"Failed to parse version {version_str}: {e}")
        return (2, 1, 2)  # 默认假设为2.1.2

def get_autocast_and_scaler():
    """
    根据PyTorch版本返回正确的autocast和GradScaler
    
    Returns:
        Tuple[autocast_class, GradScaler_class, api_type, device_support]
    """
    pytorch_version = get_pytorch_version()
    version_tuple = parse_version(pytorch_version)
    
    logger.info(f"Detecting PyTorch version: {pytorch_version}")
    
    try:
        # 对于PyTorch 2.x，尝试不同的导入策略
        if version_tuple[0] >= 2:
            # 策略1：尝试使用torch.autocast（推荐的新API）
            try:
                from torch import autocast
                from torch.cuda.amp import GradScaler
                
                # 测试autocast是否支持device_type参数
                test_device_type_support = True
                try:
                    # 创建一个测试上下文来验证API
                    with autocast(device_type='cuda', enabled=False):
                        pass
                except TypeError as e:
                    if 'device_type' in str(e):
                        test_device_type_support = False
                
                api_type = "torch_autocast_new" if test_device_type_support else "torch_autocast_old"
                logger.info(f"Using torch.autocast with device_type support: {test_device_type_support}")
                return autocast, GradScaler, api_type, test_device_type_support
                
            except (ImportError, AttributeError) as e:
                logger.warning(f"torch.autocast import failed: {e}")
            
            # 策略2：fallback到torch.cuda.amp.autocast
            try:
                from torch.cuda.amp import autocast, GradScaler
                logger.info("Using torch.cuda.amp.autocast (fallback)")
                return autocast, GradScaler, "cuda_amp", False
                
            except (ImportError, AttributeError) as e:
                logger.error(f"torch.cuda.amp import failed: {e}")
                
        else:
            # PyTorch 1.x - 使用旧API
            from torch.cuda.amp import autocast, GradScaler
            logger.info(f"Using PyTorch {pytorch_version} legacy API")
            return autocast, GradScaler, "legacy", False
            
    except Exception as e:
        logger.error(f"Failed to import autocast/GradScaler: {e}")
        raise ImportError(f"Cannot import mixed precision components: {e}")
    
    raise ImportError("No compatible autocast/GradScaler found")

def create_autocast_context(device_type: str = "cuda", enabled: bool = True, 
                          api_type: str = "torch_autocast_new", autocast_class: Any = None,
                          dtype: Optional[torch.dtype] = None):
    """
    创建兼容的autocast上下文管理器
    
    Args:
        device_type: 设备类型 ('cuda' 或 'cpu')
        enabled: 是否启用autocast
        api_type: API类型
        autocast_class: autocast类
        dtype: 数据类型（可选）
    
    Returns:
        autocast上下文管理器
    """
    if autocast_class is None:
        raise ValueError("autocast_class cannot be None")
    
    try:
        if api_type == "torch_autocast_new":
            # 新API支持device_type参数
            kwargs = {"device_type": device_type, "enabled": enabled}
            if dtype is not None:
                kwargs["dtype"] = dtype
            return autocast_class(**kwargs)
            
        elif api_type == "torch_autocast_old":
            # torch.autocast存在但不支持device_type参数
            kwargs = {"enabled": enabled}
            if dtype is not None:
                kwargs["dtype"] = dtype
            return autocast_class(**kwargs)
            
        elif api_type == "cuda_amp":
            # 使用torch.cuda.amp.autocast
            kwargs = {"enabled": enabled}
            if dtype is not None and dtype == torch.float16:
                kwargs["dtype"] = dtype
            return autocast_class(**kwargs)
            
        elif api_type == "legacy":
            # 旧版本API
            return autocast_class(enabled=enabled)
            
        else:
            raise ValueError(f"Unknown api_type: {api_type}")
            
    except Exception as e:
        logger.error(f"Failed to create autocast context: {e}")
        # 最后的fallback：尝试最简单的调用
        try:
            return autocast_class(enabled=enabled)
        except Exception as e2:
            logger.error(f"Fallback autocast creation also failed: {e2}")
            raise RuntimeError(f"Cannot create autocast context: {e}")

def create_grad_scaler(device_type: str = "cuda", GradScaler_class: Any = None):
    """
    创建兼容的GradScaler
    
    Args:
        device_type: 设备类型
        GradScaler_class: GradScaler类
    
    Returns:
        GradScaler实例
    """
    if GradScaler_class is None:
        raise ValueError("GradScaler_class cannot be None")
    
    try:
        # 尝试不传入参数的初始化方式（最兼容）
        return GradScaler_class()
    except Exception as e:
        logger.error(f"Failed to create GradScaler: {e}")
        raise RuntimeError(f"Cannot create GradScaler: {e}")

def check_mixed_precision_support():
    """
    检查混合精度训练支持情况
    
    Returns:
        Dict: 支持信息字典
    """
    support_info = {
        "pytorch_version": get_pytorch_version(),
        "cuda_available": torch.cuda.is_available(),
        "autocast_available": False,
        "gradscaler_available": False,
        "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0,
        "api_type": None,
        "device_type_support": False
    }
    
    try:
        autocast_class, GradScaler_class, api_type, device_support = get_autocast_and_scaler()
        support_info["autocast_available"] = True
        support_info["gradscaler_available"] = True
        support_info["api_type"] = api_type
        support_info["device_type_support"] = device_support
        
        # 测试基本功能
        try:
            scaler = create_grad_scaler("cuda", GradScaler_class)
            support_info["gradscaler_test"] = "success"
        except Exception as e:
            support_info["gradscaler_test"] = f"failed: {e}"
        
        try:
            ctx = create_autocast_context("cuda", True, api_type, autocast_class)
            support_info["autocast_test"] = "success"
        except Exception as e:
            support_info["autocast_test"] = f"failed: {e}"
            
    except Exception as e:
        support_info["error"] = str(e)
    
    return support_info

def log_compatibility_info():
    """记录兼容性信息到日志"""
    support_info = check_mixed_precision_support()
    logger.info("=== PyTorch Mixed Precision Compatibility Info ===")
    for key, value in support_info.items():
        logger.info(f"{key}: {value}")
    logger.info("=" * 50)
    return support_info
