# 脉冲星分类Baseline配置
# 基于物理约束的Res2Net架构
# 目标性能：94-97%准确率

experiment:
  name: "pulsar_baseline_v1"
  description: "Physics-constrained Res2Net baseline for pulsar classification"
  target_accuracy: 0.95
  random_seed: 42

# 模型配置
model:
  type: "pulsar_res2net"
  layers: [2, 3, 4, 2]
  width: 64
  scales: 3
  attention_type: "se"  # 基础注意力类型：se, eca, none
  dropout_rate: 0.2
  num_classes: 2
  zero_init_residual: false
  groups: 1

  # 增强注意力配置 - 串联架构
  enhanced_attention:
    enabled: true                    # 是否启用增强注意力
    type: ema                       # 增强注意力类型：ema, simam

    # EMA模块配置（仅当type为ema时生效）
    ema_config:
      factor: 8                      # 分组因子，必须能整除通道数

    # SimAM模块配置（仅当type为simam时生效）
    simam_config:
      e_lambda: 1e-4                 # 正则化参数

  # 通道适配器配置 - 核心创新
  channel_adapter:
    type: "physics_constrained"
    use_learnable: true
    use_edge_features: true
    edge_kernel_type: "sobel"
    normalize_weights: true
    init_weights: [1.0, 1.0, 1.0]

# 数据配置
data:
  dataset: "htru"
  data_root: "D:/PulsarSystem/datasets/HTRU"
  modality: "FPP"  # FPP or TPP
  batch_size: 64
  num_workers: 8  # 增加并行加载能力
  pin_memory: false
  analyze_loader: false  # 跳过数据加载器分析以加快启动
  val_split: 0.2
  test_split: 0.1
  random_state: 42

  # 数据增强配置 - 物理约束
  augmentation:
    train_config: "moderate"  # conservative, moderate, aggressive
    val_config: null

# 训练配置
training:
  epochs: 200

  # 优化器配置
  optimizer:
    type: "adamw"
    lr: 0.0007
    weight_decay: 0.01
    betas: [0.9, 0.999]
    eps: 0.00000001

  # 学习率调度
  scheduler:
    type: "cosine_annealing"
    T_max: 200
    eta_min: 0.000001
    warmup_epochs: 10
    warmup_lr: 0.00001

  # 损失函数配置
  loss:
    type: "focal"
    focal_alpha: [0.6, 0.4]
    focal_gamma: 2.0
    physics_constraint_weight: 0.01

  # 正则化
  regularization:
    weight_decay: 0.01
    dropout: 0.2

# 验证和保存
validation:
  eval_interval: 1
  save_best: true
  early_stopping_patience: 15
  early_stopping_min_delta: 0.0001

  # 评估指标
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "false_positive_rate"
    - "false_negative_rate"
    - "pulsar_precision"
    - "pulsar_recall"
    - "pulsar_f1"

# 日志和监控
logging:
  log_dir: "output_{modality}/logs"
  save_dir: "output_{modality}/checkpoints"
  metrics_dir: "output_{modality}/metrics"
  visualizations_dir: "output_{modality}/visualizations"
  analysis_dir: "output_{modality}/analysis"
  tensorboard: false
  log_interval: 10
  save_interval: 10

# 硬件配置
hardware:
  device: "cuda"
  mixed_precision: true
  compile_model: false
