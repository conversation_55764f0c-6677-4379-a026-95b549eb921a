# 脉冲星分类轻量级配置
# 基于物理约束的轻量级Res2Net架构
# 目标性能：97-98%准确率（更好的泛化能力）

experiment:
  name: "pulsar_baseline_v1"
  description: "Physics-constrained lightweight Res2Net for pulsar classification (recall-optimized)"
  target_recall: 0.99  # 明确以召回率为优化目标
  random_seed: 42

# 模型配置
model:
  type: "pulsar_res2net"
  layers: [1, 2, 2, 1]
  width: 32
  scales: 3
  attention_type: "se"
  dropout_rate: 0.2
  num_classes: 2
  zero_init_residual: false
  groups: 1

  # 通道适配器配置 - 核心创新
  channel_adapter:
    type: "physics_constrained"
    use_learnable: true
    use_edge_features: true
    edge_kernel_type: "sobel"
    normalize_weights: true
    init_weights: [1.0, 1.0, 1.0]

# 数据配置
data:
  dataset: "htru"
  data_root: "D:/PulsarSystem/datasets/HTRU"
  modality: "FPP"  # FPP or TPP
  batch_size: 128
  num_workers: 8  # 增加并行加载能力
  pin_memory: false
  analyze_loader: false  # 跳过数据加载器分析以加快启动
  val_split: 0.2
  test_split: 0.1
  random_state: 42

  # 数据增强配置 - 物理约束
  augmentation:
    train_config: "aggressive"  # 轻量级模型受益于更强的数据增强 moderate，aggressive
    val_config: null

# 训练配置
training:
  epochs: 200

  # 优化器配置 - 针对轻量级模型优化
  optimizer:
    type: "adamw"
    lr: 0.001  # 轻量级模型使用稍高学习率
    weight_decay: 0.01
    betas: [0.9, 0.999]
    eps: 0.00000001

  # 学习率调度
  scheduler:
    type: "cosine_annealing"
    T_max: 200
    eta_min: 0.000001
    warmup_epochs: 5  # 轻量级模型减少warmup
    warmup_lr: 0.00001

  # 损失函数配置
  loss:
    type: "focal"
    focal_alpha: [0.6, 0.4]
    focal_gamma: 2.0
    physics_constraint_weight: 0.01

  # 正则化
  regularization:
    weight_decay: 0.01
    dropout: 0.2

# 验证和保存
validation:
  eval_interval: 1
  save_best: true
  early_stopping_patience: 10  # 轻量级模型收敛更快
  early_stopping_min_delta: 0.0001

  # 评估指标 - 标准四指标（针对脉冲星分类）
  metrics:
    - "accuracy"     # 准确率：(TP+TN)/(TP+TN+FP+FN)
    - "precision"    # 精确率：TP/(TP+FP) - 脉冲星类别
    - "recall"       # 召回率：TP/(TP+FN) - 脉冲星类别
    - "f1_score"     # F1分数：精确率和召回率的调和平均
    - "auc_roc"      # AUC-ROC：额外性能指标

# 日志和监控
logging:
  log_dir: "output_{modality}/logs"
  save_dir: "output_{modality}/checkpoints"
  metrics_dir: "output_{modality}/metrics"
  visualizations_dir: "output_{modality}/visualizations"
  analysis_dir: "output_{modality}/analysis"
  tensorboard: false
  log_interval: 10
  save_interval: 10

# 硬件配置
hardware:
  device: "cuda"
  mixed_precision: true
  compile_model: false
