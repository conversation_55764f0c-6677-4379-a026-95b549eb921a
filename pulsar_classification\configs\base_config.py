"""
基础配置管理系统
提供统一的配置管理接口，支持YAML文件和字典配置

特性：
- YAML配置文件支持
- 配置验证和类型检查
- 默认值管理
- 配置合并和覆盖
- 环境变量支持
"""

import os
import yaml
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class ModelConfig:
    """模型配置"""
    type: str = "pulsar_res2net"
    layers: List[int] = field(default_factory=lambda: [3, 4, 6])
    width: int = 64
    scales: int = 4
    attention_type: str = "se"
    dropout_rate: float = 0.1
    num_classes: int = 2
    zero_init_residual: bool = False
    groups: int = 1
    
    # 通道适配器配置
    channel_adapter: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'physics_constrained',
        'use_learnable': True,
        'use_edge_features': True,
        'edge_kernel_type': 'sobel',
        'normalize_weights': True
    })


@dataclass
class DataConfig:
    """数据配置"""
    dataset: str = "htru"
    data_root: str = "../datasets/HTRU"
    modality: str = "FPP"
    batch_size: int = 32
    num_workers: int = 4
    pin_memory: bool = True
    analyze_loader: bool = False  # 是否分析数据加载器

    # 数据增强配置
    augmentation: Dict[str, Any] = field(default_factory=lambda: {
        'train_config': 'moderate',
        'val_config': None
    })

    # 数据分割配置
    val_split: float = 0.2
    test_split: float = 0.1
    random_state: int = 42


@dataclass
class TrainingConfig:
    """训练配置"""
    epochs: int = 200
    
    # 优化器配置
    optimizer: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'adamw',
        'lr': 0.001,
        'weight_decay': 0.01,
        'betas': [0.9, 0.999],
        'eps': 1e-8
    })
    
    # 学习率调度配置
    scheduler: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'cosine_annealing',
        'T_max': 200,
        'eta_min': 1e-6,
        'warmup_epochs': 10,
        'warmup_lr': 1e-5
    })
    
    # 损失函数配置
    loss: Dict[str, Any] = field(default_factory=lambda: {
        'type': 'label_smoothing_cross_entropy',
        'label_smoothing': 0.1,
        'physics_constraint_weight': 0.01
    })
    
    # 正则化配置
    regularization: Dict[str, Any] = field(default_factory=lambda: {
        'weight_decay': 0.01,
        'dropout': 0.1
    })


@dataclass
class ValidationConfig:
    """验证配置"""
    eval_interval: int = 1
    save_best: bool = True
    early_stopping_patience: int = 30
    early_stopping_min_delta: float = 0.0001
    
    # 评估指标
    metrics: List[str] = field(default_factory=lambda: [
        'accuracy', 'precision', 'recall', 'f1_score',
        'false_positive_rate', 'false_negative_rate'
    ])


@dataclass
class LoggingConfig:
    """日志配置"""
    log_dir: str = "logs"
    save_dir: str = "checkpoints"
    tensorboard: bool = True
    log_interval: int = 10
    save_interval: int = 10


@dataclass
class HardwareConfig:
    """硬件配置"""
    device: str = "cuda"
    mixed_precision: bool = True
    compile_model: bool = False


@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str = "pulsar_baseline_v1"
    description: str = "Physics-constrained Res2Net baseline for pulsar classification"
    target_accuracy: float = 0.95
    random_seed: int = 42


@dataclass
class PulsarConfig:
    """完整的脉冲星分类配置"""
    experiment: ExperimentConfig = field(default_factory=ExperimentConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    data: DataConfig = field(default_factory=DataConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    validation: ValidationConfig = field(default_factory=ValidationConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    hardware: HardwareConfig = field(default_factory=HardwareConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path
        self.config = PulsarConfig()
        
        if config_path and os.path.exists(config_path):
            self.load_from_yaml(config_path)
    
    def load_from_yaml(self, yaml_path: str) -> None:
        """从YAML文件加载配置"""
        with open(yaml_path, 'r', encoding='utf-8') as f:
            yaml_config = yaml.safe_load(f)
        
        self.update_from_dict(yaml_config)
    
    def save_to_yaml(self, yaml_path: str) -> None:
        """保存配置到YAML文件"""
        config_dict = self.to_dict()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(yaml_path), exist_ok=True)
        
        with open(yaml_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def update_from_dict(self, config_dict: Dict[str, Any]) -> None:
        """从字典更新配置"""
        for section_name, section_config in config_dict.items():
            if hasattr(self.config, section_name):
                section = getattr(self.config, section_name)
                self._update_section(section, section_config)
    
    def _update_section(self, section: Any, config: Dict[str, Any]) -> None:
        """更新配置段"""
        for key, value in config.items():
            if hasattr(section, key):
                current_value = getattr(section, key)
                if isinstance(current_value, dict) and isinstance(value, dict):
                    # 递归更新字典
                    current_value.update(value)
                else:
                    setattr(section, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        def dataclass_to_dict(obj):
            if hasattr(obj, '__dataclass_fields__'):
                result = {}
                for field_name in obj.__dataclass_fields__:
                    field_value = getattr(obj, field_name)
                    result[field_name] = dataclass_to_dict(field_value)
                return result
            elif isinstance(obj, dict):
                return {k: dataclass_to_dict(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [dataclass_to_dict(item) for item in obj]
            else:
                return obj
        
        return dataclass_to_dict(self.config)
    
    def validate(self) -> List[str]:
        """验证配置的有效性"""
        errors = []
        
        # 验证模型配置
        if self.config.model.num_classes <= 0:
            errors.append("model.num_classes must be positive")
        
        if self.config.model.dropout_rate < 0 or self.config.model.dropout_rate > 1:
            errors.append("model.dropout_rate must be between 0 and 1")
        
        # 验证数据配置
        if self.config.data.batch_size <= 0:
            errors.append("data.batch_size must be positive")
        
        if not os.path.exists(self.config.data.data_root):
            errors.append(f"data.data_root does not exist: {self.config.data.data_root}")
        
        # 验证训练配置
        if self.config.training.epochs <= 0:
            errors.append("training.epochs must be positive")
        
        if self.config.training.optimizer['lr'] <= 0:
            errors.append("training.optimizer.lr must be positive")
        
        return errors
    
    def get_section(self, section_name: str) -> Any:
        """获取配置段"""
        return getattr(self.config, section_name)
    
    def set_section(self, section_name: str, section_config: Any) -> None:
        """设置配置段"""
        setattr(self.config, section_name, section_config)


def load_config(config_path: str) -> PulsarConfig:
    """加载配置文件"""
    manager = ConfigManager(config_path)

    # 动态替换路径中的模态变量
    _resolve_dynamic_paths(manager.config)

    # 验证配置
    errors = manager.validate()
    if errors:
        raise ValueError(f"Configuration validation failed:\n" + "\n".join(errors))

    return manager.config


def _resolve_dynamic_paths(config: PulsarConfig) -> None:
    """解析动态路径，替换{modality}等变量"""
    modality = config.data.modality

    # 替换日志配置中的动态路径
    if hasattr(config.logging, 'log_dir') and '{modality}' in config.logging.log_dir:
        config.logging.log_dir = config.logging.log_dir.format(modality=modality)

    if hasattr(config.logging, 'save_dir') and '{modality}' in config.logging.save_dir:
        config.logging.save_dir = config.logging.save_dir.format(modality=modality)

    if hasattr(config.logging, 'metrics_dir') and '{modality}' in config.logging.metrics_dir:
        config.logging.metrics_dir = config.logging.metrics_dir.format(modality=modality)

    if hasattr(config.logging, 'visualizations_dir') and '{modality}' in config.logging.visualizations_dir:
        config.logging.visualizations_dir = config.logging.visualizations_dir.format(modality=modality)

    if hasattr(config.logging, 'analysis_dir') and '{modality}' in config.logging.analysis_dir:
        config.logging.analysis_dir = config.logging.analysis_dir.format(modality=modality)


def create_default_config() -> PulsarConfig:
    """创建默认配置"""
    return PulsarConfig()


def merge_configs(base_config: PulsarConfig, override_config: Dict[str, Any]) -> PulsarConfig:
    """合并配置"""
    manager = ConfigManager()
    manager.config = base_config
    manager.update_from_dict(override_config)
    return manager.config


# 导出的公共接口
__all__ = [
    'ModelConfig',
    'DataConfig', 
    'TrainingConfig',
    'ValidationConfig',
    'LoggingConfig',
    'HardwareConfig',
    'ExperimentConfig',
    'PulsarConfig',
    'ConfigManager',
    'load_config',
    'create_default_config',
    'merge_configs'
]
