{"experiment": "ExperimentConfig(name='PulsarSystem', description='Physics-constrained lightweight Res2Net for pulsar classification (recall-optimized)', target_accuracy=0.95, random_seed=42)", "model": "ModelConfig(type='pulsar_res2net', layers=[1, 2, 2, 1], width=32, scales=3, attention_type='se', dropout_rate=0.2, num_classes=2, zero_init_residual=False, groups=1, channel_adapter={'type': 'physics_constrained', 'use_learnable': True, 'use_edge_features': True, 'edge_kernel_type': 'sobel', 'normalize_weights': True, 'init_weights': [1.0, 1.0, 1.0]})", "data": "DataConfig(dataset='htru', data_root='/yanyb/jmy/PulsarSystem/datasets/HTRU', modality='TPP', batch_size=128, num_workers=8, pin_memory=False, analyze_loader=False, augmentation={'train_config': 'aggressive', 'val_config': None}, val_split=0.2, test_split=0.1, random_state=42)", "training": "TrainingConfig(epochs=200, optimizer={'type': 'adamw', 'lr': 0.001, 'weight_decay': 0.01, 'betas': [0.9, 0.999], 'eps': 1e-08}, scheduler={'type': 'cosine_annealing', 'T_max': 200, 'eta_min': 1e-06, 'warmup_epochs': 5, 'warmup_lr': 1e-05}, loss={'type': 'focal', 'label_smoothing': 0.1, 'physics_constraint_weight': 0.01, 'focal_alpha': [0.6, 0.4], 'focal_gamma': 2.0}, regularization={'weight_decay': 0.01, 'dropout': 0.2})", "validation": "ValidationConfig(eval_interval=1, save_best=True, early_stopping_patience=10, early_stopping_min_delta=0.0001, metrics=['accuracy', 'precision', 'recall', 'f1_score', 'false_positive_rate', 'false_negative_rate', 'pulsar_precision', 'pulsar_recall', 'pulsar_f1'])", "logging": "LoggingConfig(log_dir='output_TPP/logs', save_dir='output_TPP/checkpoints', tensorboard=False, log_interval=10, save_interval=10)", "hardware": "HardwareConfig(device='cuda', mixed_precision=True, compile_model=False)"}