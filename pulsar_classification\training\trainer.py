"""
基础训练器模块
实现脉冲星分类模型的训练和验证逻辑

特性：
- 支持混合精度训练
- 早停机制
- 学习率调度
- 检查点保存和恢复
- 详细的训练日志
- 物理约束损失集成
"""

import os
import time
from typing import Dict, Any, Optional, Tuple
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import logging
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.pytorch_compat import (
    get_autocast_and_scaler,
    create_autocast_context,
    create_grad_scaler,
    check_mixed_precision_support,
    log_compatibility_info
)
from .losses import create_loss_function
from .metrics import MetricsCalculator, AverageMeter


class PulsarTrainer:
    """
    脉冲星分类训练器

    Args:
        model: 脉冲星分类模型
        train_loader: 训练数据加载器
        val_loader: 验证数据加载器
        config: 训练配置
        device: 训练设备
        logger: 日志记录器
    """

    def __init__(
        self,
        model: nn.Module,
        train_loader: DataLoader,
        val_loader: DataLoader,
        config: Dict[str, Any],
        device: torch.device,
        logger: Optional[logging.Logger] = None
    ):
        self.model = model
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.config = config
        self.device = device
        self.logger = logger or logging.getLogger(__name__)

        # 训练状态
        self.current_epoch = 0
        self.best_metric = 0.0
        self.best_epoch = 0
        self.early_stopping_counter = 0

        # 设置优化器
        self.optimizer = self._create_optimizer()

        # 设置学习率调度器
        self.scheduler = self._create_scheduler()

        # 设置损失函数
        self.criterion = create_loss_function(config.get('loss', {}))

        # 混合精度训练兼容性处理
        self.use_amp = config.get('mixed_precision', False)
        if self.use_amp:
            # 记录兼容性信息
            support_info = log_compatibility_info()

            # 获取兼容的autocast和GradScaler
            self.autocast_class, self.GradScaler_class, self.api_type, self.device_type_support = get_autocast_and_scaler()

            # 创建GradScaler
            self.scaler = create_grad_scaler(self.device.type, self.GradScaler_class)

            # 设置设备类型
            self.autocast_device_type = 'cuda' if self.device.type == 'cuda' else 'cpu'

            self.logger.info(f"Mixed precision enabled with API type: {self.api_type}")
            self.logger.info(f"Device type support: {self.device_type_support}")
        else:
            self.scaler = None
            self.autocast_class = None
            self.api_type = None
            self.device_type_support = False
            self.autocast_device_type = None

        # 早停配置
        self.early_stopping_patience = config.get('early_stopping_patience', 30)
        self.early_stopping_min_delta = config.get('early_stopping_min_delta', 0.0001)

        # 梯度裁剪配置
        self.max_grad_norm = config.get('max_grad_norm', 1.0)
        self.enable_grad_clip = config.get('enable_grad_clip', True)

        # Warmup配置
        self.warmup_epochs = config.get('warmup_epochs', 10)
        self.warmup_lr = config.get('warmup_lr', 1e-5)
        self.base_lr = self.optimizer.param_groups[0]['lr']

        # 保存配置
        self.save_dir = config.get('save_dir', 'checkpoints')
        os.makedirs(self.save_dir, exist_ok=True)

        # 日志配置
        self.log_interval = config.get('log_interval', 10)
        self.eval_interval = config.get('eval_interval', 1)

    def _create_optimizer(self) -> torch.optim.Optimizer:
        """创建优化器"""
        optimizer_config = self.config.get('optimizer', {})
        optimizer_type = optimizer_config.get('type', 'adamw').lower()

        if optimizer_type == 'adamw':
            return torch.optim.AdamW(
                self.model.parameters(),
                lr=optimizer_config.get('lr', 0.001),
                weight_decay=optimizer_config.get('weight_decay', 0.01),
                betas=optimizer_config.get('betas', [0.9, 0.999]),
                eps=optimizer_config.get('eps', 1e-8)
            )
        elif optimizer_type == 'adam':
            return torch.optim.Adam(
                self.model.parameters(),
                lr=optimizer_config.get('lr', 0.001),
                weight_decay=optimizer_config.get('weight_decay', 0.01),
                betas=optimizer_config.get('betas', [0.9, 0.999]),
                eps=optimizer_config.get('eps', 1e-8)
            )
        elif optimizer_type == 'sgd':
            return torch.optim.SGD(
                self.model.parameters(),
                lr=optimizer_config.get('lr', 0.01),
                momentum=optimizer_config.get('momentum', 0.9),
                weight_decay=optimizer_config.get('weight_decay', 1e-4)
            )
        else:
            raise ValueError(f"Unsupported optimizer type: {optimizer_type}")

    def _create_scheduler(self) -> Optional[torch.optim.lr_scheduler._LRScheduler]:
        """创建学习率调度器"""
        scheduler_config = self.config.get('scheduler', {})
        scheduler_type = scheduler_config.get('type', 'cosine_annealing').lower()

        if scheduler_type == 'cosine_annealing':
            return torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=scheduler_config.get('T_max', 200),
                eta_min=scheduler_config.get('eta_min', 1e-6)
            )
        elif scheduler_type == 'step':
            return torch.optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=scheduler_config.get('step_size', 30),
                gamma=scheduler_config.get('gamma', 0.1)
            )
        elif scheduler_type == 'reduce_on_plateau':
            return torch.optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='max',
                factor=scheduler_config.get('factor', 0.5),
                patience=scheduler_config.get('patience', 10),
                min_lr=scheduler_config.get('min_lr', 1e-6)
            )
        elif scheduler_type == 'none':
            return None
        else:
            raise ValueError(f"Unsupported scheduler type: {scheduler_type}")

    def train_epoch(self) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()

        # 指标记录
        loss_meter = AverageMeter()
        metrics_calculator = MetricsCalculator(num_classes=2)

        start_time = time.time()

        for batch_idx, (inputs, targets) in enumerate(self.train_loader):
            inputs, targets = inputs.to(self.device), targets.to(self.device)

            # 前向传播
            if self.use_amp:
                with create_autocast_context(
                    device_type=self.autocast_device_type,
                    enabled=True,
                    api_type=self.api_type,
                    autocast_class=self.autocast_class
                ):
                    outputs = self.model(inputs)

                    # 计算损失
                    if hasattr(self.criterion, 'forward') and 'original_input' in self.criterion.forward.__code__.co_varnames:
                        # 支持物理约束损失的损失函数
                        adapted_input = self.model.channel_adapter(inputs) if hasattr(self.model, 'channel_adapter') else None
                        loss_dict = self.criterion(outputs, targets, inputs, adapted_input)
                        loss = loss_dict['total_loss']
                    else:
                        loss = self.criterion(outputs, targets)
            else:
                outputs = self.model(inputs)

                if hasattr(self.criterion, 'forward') and 'original_input' in self.criterion.forward.__code__.co_varnames:
                    adapted_input = self.model.channel_adapter(inputs) if hasattr(self.model, 'channel_adapter') else None
                    loss_dict = self.criterion(outputs, targets, inputs, adapted_input)
                    loss = loss_dict['total_loss']
                else:
                    loss = self.criterion(outputs, targets)

            # 反向传播
            self.optimizer.zero_grad()

            if self.use_amp:
                self.scaler.scale(loss).backward()

                # 梯度裁剪（在scaler.step之前）
                if self.enable_grad_clip:
                    self.scaler.unscale_(self.optimizer)
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)

                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                loss.backward()

                # 梯度裁剪
                if self.enable_grad_clip:
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)

                self.optimizer.step()

            # 更新指标
            loss_meter.update(loss.item(), inputs.size(0))
            metrics_calculator.update(outputs, targets)

            # 日志记录
            if batch_idx % self.log_interval == 0:
                self.logger.info(
                    f'Train Epoch: {self.current_epoch} [{batch_idx * len(inputs)}/{len(self.train_loader.dataset)} '
                    f'({100. * batch_idx / len(self.train_loader):.0f}%)]\t'
                    f'Loss: {loss.item():.6f}'
                )

        # 计算epoch指标
        epoch_metrics = metrics_calculator.compute()
        epoch_metrics['loss'] = loss_meter.avg
        epoch_metrics['time'] = time.time() - start_time

        return epoch_metrics

    def validate(self) -> Dict[str, float]:
        """验证模型"""
        self.model.eval()

        loss_meter = AverageMeter()
        metrics_calculator = MetricsCalculator(num_classes=2)

        with torch.no_grad():
            for inputs, targets in self.val_loader:
                inputs, targets = inputs.to(self.device), targets.to(self.device)

                if self.use_amp:
                    with create_autocast_context(
                        device_type=self.autocast_device_type,
                        enabled=True,
                        api_type=self.api_type,
                        autocast_class=self.autocast_class
                    ):
                        outputs = self.model(inputs)

                        if hasattr(self.criterion, 'forward') and 'original_input' in self.criterion.forward.__code__.co_varnames:
                            adapted_input = self.model.channel_adapter(inputs) if hasattr(self.model, 'channel_adapter') else None
                            loss_dict = self.criterion(outputs, targets, inputs, adapted_input)
                            loss = loss_dict['total_loss']
                        else:
                            loss = self.criterion(outputs, targets)
                else:
                    outputs = self.model(inputs)

                    if hasattr(self.criterion, 'forward') and 'original_input' in self.criterion.forward.__code__.co_varnames:
                        adapted_input = self.model.channel_adapter(inputs) if hasattr(self.model, 'channel_adapter') else None
                        loss_dict = self.criterion(outputs, targets, inputs, adapted_input)
                        loss = loss_dict['total_loss']
                    else:
                        loss = self.criterion(outputs, targets)

                loss_meter.update(loss.item(), inputs.size(0))
                metrics_calculator.update(outputs, targets)

        val_metrics = metrics_calculator.compute()
        val_metrics['loss'] = loss_meter.avg

        return val_metrics

    def save_checkpoint(self, metrics: Dict[str, float], is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'scaler_state_dict': self.scaler.state_dict() if self.scaler else None,
            'metrics': metrics,
            'config': self.config
        }

        # 保存最新检查点
        checkpoint_path = os.path.join(self.save_dir, 'latest_checkpoint.pth')
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.save_dir, 'best_checkpoint.pth')
            torch.save(checkpoint, best_path)
            validation_config = getattr(self.config, 'validation', {})
            early_stopping_metric = validation_config.get('early_stopping_metric', 'recall')
            self.logger.info(f'Saved best checkpoint with {early_stopping_metric}: {metrics.get(early_stopping_metric, 0.0):.4f}')

    def _apply_warmup(self, epoch: int):
        """应用学习率warmup"""
        if epoch < self.warmup_epochs:
            # 线性warmup
            warmup_factor = (epoch + 1) / self.warmup_epochs
            current_lr = self.warmup_lr + (self.base_lr - self.warmup_lr) * warmup_factor

            for param_group in self.optimizer.param_groups:
                param_group['lr'] = current_lr

            self.logger.debug(f'Warmup epoch {epoch}: lr = {current_lr:.6f}')

    def train(self, num_epochs: int) -> Dict[str, Any]:
        """训练模型"""
        self.logger.info(f'Starting training for {num_epochs} epochs')

        training_history = {
            'train_metrics': [],
            'val_metrics': [],
            'learning_rates': []
        }

        for epoch in range(num_epochs):
            self.current_epoch = epoch

            # 应用warmup
            self._apply_warmup(epoch)

            # 训练
            train_metrics = self.train_epoch()
            training_history['train_metrics'].append(train_metrics)

            # 验证
            if epoch % self.eval_interval == 0:
                val_metrics = self.validate()
                training_history['val_metrics'].append(val_metrics)

                # 检查是否为最佳模型（基于配置的早停指标）
                validation_config = getattr(self.config, 'validation', {})
                early_stopping_metric = validation_config.get('early_stopping_metric', 'recall')
                current_metric = val_metrics.get(early_stopping_metric, 0.0)
                is_best = current_metric > self.best_metric

                # 调试信息：显示当前使用的指标
                if epoch == 0:  # 只在第一轮显示一次
                    self.logger.info(f'Using early stopping metric: {early_stopping_metric}')

                if is_best:
                    self.best_metric = current_metric
                    self.best_epoch = epoch
                    self.early_stopping_counter = 0
                else:
                    self.early_stopping_counter += 1

                # 保存检查点
                self.save_checkpoint(val_metrics, is_best)

                # 日志记录 - 显示关键指标
                validation_config = getattr(self.config, 'validation', {})
                early_stopping_metric = validation_config.get('early_stopping_metric', 'recall')
                self.logger.info(
                    f'Epoch {epoch}: Train Loss: {train_metrics["loss"]:.4f}, '
                    f'Train {early_stopping_metric.title()}: {train_metrics.get(early_stopping_metric, 0.0):.4f}, '
                    f'Val Loss: {val_metrics["loss"]:.4f}, '
                    f'Val {early_stopping_metric.title()}: {val_metrics.get(early_stopping_metric, 0.0):.4f}'
                )

                # 早停检查
                if self.early_stopping_counter >= self.early_stopping_patience:
                    self.logger.info(f'Early stopping triggered after {epoch} epochs')
                    break

            # 学习率调度（warmup期间不调用调度器）
            if self.scheduler and epoch >= self.warmup_epochs:
                if isinstance(self.scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    # 使用召回率调度学习率，与优化目标一致
                    self.scheduler.step(val_metrics.get('recall', 0.0))
                else:
                    self.scheduler.step()

            # 记录学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            training_history['learning_rates'].append(current_lr)

        validation_config = getattr(self.config, 'validation', {})
        early_stopping_metric = validation_config.get('early_stopping_metric', 'recall')
        self.logger.info(f'Training completed. Best {early_stopping_metric}: {self.best_metric:.4f} at epoch {self.best_epoch}')

        return training_history


# 导出的公共接口
__all__ = [
    'PulsarTrainer'
]
