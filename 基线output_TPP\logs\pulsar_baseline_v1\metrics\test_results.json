{"test_metrics": {"accuracy": 0.9921836834391793, "precision": 0.9921836834391793, "recall": 0.9921836834391793, "f1_score": 0.9921836834391793, "true_positive_rate": 0.9617224880382775, "false_positive_rate": 0.004352557127312296, "false_negative_rate": 0.03827751196172249, "true_negative_rate": 0.9956474428726877, "pulsar_precision": 0.9617224880382775, "pulsar_recall": 0.9617224880382775, "pulsar_f1": 0.9617224880382775, "auc_roc": 0.9990524337354416}, "confusion_matrix": [[1830, 8], [8, 201]], "classification_report": "              precision    recall  f1-score   support\n\n  non_pulsar       1.00      1.00      1.00      1838\n      pulsar       0.96      0.96      0.96       209\n\n    accuracy                           0.99      2047\n   macro avg       0.98      0.98      0.98      2047\nweighted avg       0.99      0.99      0.99      2047\n", "misclassified_count": 16, "total_test_samples": 2047}