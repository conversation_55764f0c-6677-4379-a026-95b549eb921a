"""
EMA (Efficient Multi-Scale Attention) Module
From: Efficient Multi-Scale Attention Module with Cross-Spatial Learning (ICCASSP 2023)

Integrated for PulsarRes2Net architecture with mixed precision compatibility.
"""

import torch
from torch import nn


class EMAModule(nn.Module):
    """
    Efficient Multi-Scale Attention Module

    Args:
        channels: Input channel number
        factor: Group factor for multi-scale processing (default: 8)
    """
    def __init__(self, channels: int, factor: int = 8):
        super(EMAModule, self).__init__()
        self.groups = factor
        assert channels // self.groups > 0, f"channels ({channels}) must be divisible by factor ({factor})"

        self.softmax = nn.Softmax(-1)
        self.agp = nn.AdaptiveAvgPool2d((1, 1))
        self.pool_h = nn.AdaptiveAvgPool2d((None, 1))
        self.pool_w = nn.AdaptiveAvgPool2d((1, None))
        self.gn = nn.GroupNorm(channels // self.groups, channels // self.groups)
        self.conv1x1 = nn.Conv2d(channels // self.groups, channels // self.groups, kernel_size=1, stride=1, padding=0)
        self.conv3x3 = nn.Conv2d(channels // self.groups, channels // self.groups, kernel_size=3, stride=1, padding=1)

    def forward(self, x):
        b, c, h, w = x.size()

        # 内存优化：使用更小的中间张量
        group_x = x.reshape(b * self.groups, -1, h, w)  # b*g,c//g,h,w

        # 池化操作
        x_h = self.pool_h(group_x)
        x_w = self.pool_w(group_x).permute(0, 1, 3, 2)

        # 1x1卷积和分割
        hw = self.conv1x1(torch.cat([x_h, x_w], dim=2))
        x_h, x_w = torch.split(hw, [h, w], dim=2)

        # 第一分支：GroupNorm + 注意力
        x1 = self.gn(group_x * x_h.sigmoid() * x_w.permute(0, 1, 3, 2).sigmoid())

        # 第二分支：3x3卷积
        x2 = self.conv3x3(group_x)

        # 内存优化：分别计算矩阵乘法以减少峰值内存使用
        # 第一个矩阵乘法
        x11 = self.softmax(self.agp(x1).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x12 = x2.reshape(b * self.groups, c // self.groups, -1)  # b*g, c//g, hw
        weights1 = torch.matmul(x11, x12)

        # 清理中间变量以释放内存
        del x11, x12

        # 第二个矩阵乘法
        x21 = self.softmax(self.agp(x2).reshape(b * self.groups, -1, 1).permute(0, 2, 1))
        x22 = x1.reshape(b * self.groups, c // self.groups, -1)  # b*g, c//g, hw
        weights2 = torch.matmul(x21, x22)

        # 清理中间变量
        del x21, x22, x1, x2

        # 合并权重并重塑
        weights = (weights1 + weights2).reshape(b * self.groups, 1, h, w)
        del weights1, weights2

        # 最终输出
        result = (group_x * weights.sigmoid()).reshape(b, c, h, w)

        # 清理最后的中间变量
        del group_x, weights

        return result
