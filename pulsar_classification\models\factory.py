"""
模型工厂函数
提供统一的模型创建接口，支持配置驱动的模型实例化

基于PyTorch最佳实践，提供：
- 配置驱动的模型创建
- 预训练权重加载
- 模型验证和错误处理
"""

from typing import Dict, Any, Optional, Union
import torch
import torch.nn as nn
from torch import Tensor

from .pulsar_res2net import PulsarRes2Net
from .channel_adapters import create_channel_adapter
from .attention import create_attention_module


def create_pulsar_res2net(config: Dict[str, Any]) -> PulsarRes2Net:
    """
    创建脉冲星Res2Net模型
    
    Args:
        config: 模型配置字典，包含以下键：
            - layers: 每层的块数 (default: [3, 4, 6])
            - num_classes: 分类数 (default: 2)
            - width: 基础宽度 (default: 64)
            - scales: Res2Net尺度数 (default: 4)
            - attention_type: 注意力类型 (default: 'se')
            - channel_adapter: 通道适配器配置
            - dropout_rate: Dropout比例 (default: 0.1)
            
    Returns:
        PulsarRes2Net模型实例
        
    Example:
        >>> config = {
        ...     'layers': [3, 4, 6],
        ...     'width': 64,
        ...     'scales': 4,
        ...     'attention_type': 'se',
        ...     'channel_adapter': {
        ...         'type': 'physics_constrained',
        ...         'use_learnable': True,
        ...         'use_edge_features': True
        ...     }
        ... }
        >>> model = create_pulsar_res2net(config)
    """
    return PulsarRes2Net(
        layers=config.get('layers', [3, 4, 6]),
        num_classes=config.get('num_classes', 2),
        zero_init_residual=config.get('zero_init_residual', False),
        groups=config.get('groups', 1),
        width=config.get('width', 64),
        scales=config.get('scales', 4),
        attention_type=config.get('attention_type', 'se'),
        channel_adapter_config=config.get('channel_adapter', None),
        dropout_rate=config.get('dropout_rate', 0.1)
    )


def load_model_from_checkpoint(
    config: Dict[str, Any],
    checkpoint_path: str,
    device: Optional[Union[str, torch.device]] = None,
    strict: bool = True
) -> PulsarRes2Net:
    """
    从检查点加载模型
    
    Args:
        config: 模型配置
        checkpoint_path: 检查点文件路径
        device: 目标设备
        strict: 是否严格匹配权重
        
    Returns:
        加载权重后的模型
    """
    # 创建模型
    model = create_pulsar_res2net(config)
    
    # 加载检查点
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    
    # 提取模型权重
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    elif 'state_dict' in checkpoint:
        state_dict = checkpoint['state_dict']
    else:
        state_dict = checkpoint
    
    # 加载权重
    model.load_state_dict(state_dict, strict=strict)
    model.to(device)
    
    return model


def validate_model_config(config: Dict[str, Any]) -> bool:
    """
    验证模型配置的有效性
    
    Args:
        config: 模型配置字典
        
    Returns:
        配置是否有效
    """
    try:
        # 检查必要的配置项
        layers = config.get('layers', [3, 4, 6])
        if not isinstance(layers, list) or len(layers) == 0:
            return False
        
        width = config.get('width', 64)
        if not isinstance(width, int) or width <= 0:
            return False
        
        scales = config.get('scales', 4)
        if not isinstance(scales, int) or scales <= 0:
            return False
        
        num_classes = config.get('num_classes', 2)
        if not isinstance(num_classes, int) or num_classes <= 0:
            return False
        
        # 检查通道适配器配置
        channel_adapter = config.get('channel_adapter', {})
        if channel_adapter:
            adapter_type = channel_adapter.get('type', 'physics_constrained')
            if adapter_type not in ['physics_constrained', 'simple']:
                return False
        
        return True
        
    except Exception:
        return False


def get_model_info(model: nn.Module) -> Dict[str, Any]:
    """
    获取模型信息
    
    Args:
        model: PyTorch模型
        
    Returns:
        模型信息字典
    """
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    def get_model_size_mb(model):
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / 1024 / 1024
        return size_mb
    
    info = {
        'total_parameters': count_parameters(model),
        'model_size_mb': get_model_size_mb(model),
        'model_type': type(model).__name__
    }
    
    # 如果是PulsarRes2Net，获取额外信息
    if isinstance(model, PulsarRes2Net):
        adapter_weights = model.get_channel_adapter_weights()
        if adapter_weights:
            info['channel_adapter_weights'] = adapter_weights
    
    return info


# 预定义的模型配置
PREDEFINED_CONFIGS = {
    'baseline': {
        'layers': [1, 1, 2],  # 减少层数：从[3,4,6]到[2,3,4]
        'num_classes': 2,
        'width': 24,  # 减少宽度：从64到48
        'scales': 4,  # 保持多尺度特性
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'sobel',
            'normalize_weights': True
        }
    },
    'lightweight': {
        'layers': [2, 2, 2],
        'num_classes': 2,
        'width': 32,
        'scales': 4,
        'attention_type': 'se',
        'dropout_rate': 0.1,
        'channel_adapter': {
            'type': 'simple',
            'strategy': 'repeat'
        }
    },
    'high_capacity': {
        'layers': [3, 4, 23],
        'num_classes': 2,
        'width': 128,
        'scales': 4,
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'learnable',
            'normalize_weights': True
        }
    }
}


def create_model_from_preset(preset_name: str, **kwargs) -> PulsarRes2Net:
    """
    从预定义配置创建模型
    
    Args:
        preset_name: 预设配置名称 ('baseline', 'lightweight', 'high_capacity')
        **kwargs: 覆盖的配置参数
        
    Returns:
        模型实例
    """
    if preset_name not in PREDEFINED_CONFIGS:
        raise ValueError(f"Unknown preset: {preset_name}. Available: {list(PREDEFINED_CONFIGS.keys())}")
    
    config = PREDEFINED_CONFIGS[preset_name].copy()
    config.update(kwargs)
    
    return create_pulsar_res2net(config)


# 导出的公共接口
__all__ = [
    'create_pulsar_res2net',
    'load_model_from_checkpoint',
    'validate_model_config',
    'get_model_info',
    'create_model_from_preset',
    'PREDEFINED_CONFIGS'
]
