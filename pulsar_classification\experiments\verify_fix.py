"""
验证配置传递Bug修复效果的脚本

测试修复后的train_baseline.py是否能正确传递enhanced_attention配置，
并验证模型参数数量是否符合预期。

使用方法：
    python verify_fix.py
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
from configs.base_config import load_config
from models.factory import create_pulsar_res2net, get_model_info


def test_enhanced_attention_config_passing():
    """测试enhanced_attention配置传递是否正常"""
    print("=== 测试enhanced_attention配置传递 ===")
    
    # 测试FPP+EMA配置
    print("\n1. 测试FPP+EMA配置")
    fpp_ema_config = {
        'layers': [2, 3, 4, 2],
        'num_classes': 2,
        'width': 64,
        'scales': 3,
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'zero_init_residual': False,
        'groups': 1,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'sobel',
            'normalize_weights': True,
            'init_weights': [1.0, 1.0, 1.0]
        },
        'enhanced_attention': {
            'enabled': True,
            'type': 'ema',
            'ema_config': {'factor': 8},
            'simam_config': {'e_lambda': 1e-4}
        }
    }
    
    model_fpp = create_pulsar_res2net(fpp_ema_config)
    model_info_fpp = get_model_info(model_fpp)
    
    print(f"  - 模型参数数量: {model_info_fpp['total_parameters']:,}")
    print(f"  - 模型大小: {model_info_fpp['model_size_mb']:.2f} MB")
    
    # 验证参数数量是否符合预期（SE+EMA应该有更多参数）
    expected_fpp_params = 46257230  # 预期的FPP+EMA参数数量
    actual_fpp_params = model_info_fpp['total_parameters']
    
    if actual_fpp_params == expected_fpp_params:
        print(f"  ✅ FPP+EMA参数数量验证通过: {actual_fpp_params:,}")
    else:
        print(f"  ❌ FPP+EMA参数数量不匹配: 预期 {expected_fpp_params:,}, 实际 {actual_fpp_params:,}")
    
    # 测试TPP+SimAM配置
    print("\n2. 测试TPP+SimAM配置")
    tpp_simam_config = {
        'layers': [2, 3, 4, 2],
        'num_classes': 2,
        'width': 64,
        'scales': 3,
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'zero_init_residual': False,
        'groups': 1,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'sobel',
            'normalize_weights': True,
            'init_weights': [1.0, 1.0, 1.0]
        },
        'enhanced_attention': {
            'enabled': True,
            'type': 'simam',
            'ema_config': {'factor': 8},
            'simam_config': {'e_lambda': 1e-4}
        }
    }
    
    model_tpp = create_pulsar_res2net(tpp_simam_config)
    model_info_tpp = get_model_info(model_tpp)
    
    print(f"  - 模型参数数量: {model_info_tpp['total_parameters']:,}")
    print(f"  - 模型大小: {model_info_tpp['model_size_mb']:.2f} MB")
    
    # 验证参数数量是否符合预期（SE+SimAM应该与baseline相同）
    expected_tpp_params = 39059534  # 预期的TPP+SimAM参数数量
    actual_tpp_params = model_info_tpp['total_parameters']
    
    if actual_tpp_params == expected_tpp_params:
        print(f"  ✅ TPP+SimAM参数数量验证通过: {actual_tpp_params:,}")
    else:
        print(f"  ❌ TPP+SimAM参数数量不匹配: 预期 {expected_tpp_params:,}, 实际 {actual_tpp_params:,}")
    
    # 测试baseline配置（enhanced_attention禁用）
    print("\n3. 测试baseline配置（enhanced_attention禁用）")
    baseline_config = {
        'layers': [2, 3, 4, 2],
        'num_classes': 2,
        'width': 64,
        'scales': 3,
        'attention_type': 'se',
        'dropout_rate': 0.2,
        'zero_init_residual': False,
        'groups': 1,
        'channel_adapter': {
            'type': 'physics_constrained',
            'use_learnable': True,
            'use_edge_features': True,
            'edge_kernel_type': 'sobel',
            'normalize_weights': True,
            'init_weights': [1.0, 1.0, 1.0]
        },
        'enhanced_attention': {
            'enabled': False,
            'type': None,
            'ema_config': {'factor': 8},
            'simam_config': {'e_lambda': 1e-4}
        }
    }
    
    model_baseline = create_pulsar_res2net(baseline_config)
    model_info_baseline = get_model_info(model_baseline)
    
    print(f"  - 模型参数数量: {model_info_baseline['total_parameters']:,}")
    print(f"  - 模型大小: {model_info_baseline['model_size_mb']:.2f} MB")
    
    # 验证参数数量是否符合预期（仅SE应该与TPP+SimAM相同）
    expected_baseline_params = 39059534  # 预期的baseline参数数量
    actual_baseline_params = model_info_baseline['total_parameters']
    
    if actual_baseline_params == expected_baseline_params:
        print(f"  ✅ Baseline参数数量验证通过: {actual_baseline_params:,}")
    else:
        print(f"  ❌ Baseline参数数量不匹配: 预期 {expected_baseline_params:,}, 实际 {actual_baseline_params:,}")
    
    return {
        'fpp_ema_params': actual_fpp_params,
        'tpp_simam_params': actual_tpp_params,
        'baseline_params': actual_baseline_params,
        'fpp_ema_correct': actual_fpp_params == expected_fpp_params,
        'tpp_simam_correct': actual_tpp_params == expected_tpp_params,
        'baseline_correct': actual_baseline_params == expected_baseline_params
    }


def test_config_file_loading():
    """测试配置文件加载是否正常"""
    print("\n=== 测试配置文件加载 ===")
    
    # 测试fpp_ema.yaml配置文件
    print("\n1. 测试fpp_ema.yaml配置文件")
    try:
        fpp_config_path = project_root / "configs" / "fpp_ema.yaml"
        if fpp_config_path.exists():
            fpp_config = load_config(str(fpp_config_path))
            print(f"  ✅ fpp_ema.yaml加载成功")
            print(f"  - 模态: {fpp_config.data.modality}")
            print(f"  - enhanced_attention.enabled: {fpp_config.model.enhanced_attention.get('enabled')}")
            print(f"  - enhanced_attention.type: {fpp_config.model.enhanced_attention.get('type')}")
        else:
            print(f"  ❌ fpp_ema.yaml文件不存在: {fpp_config_path}")
    except Exception as e:
        print(f"  ❌ fpp_ema.yaml加载失败: {e}")
    
    # 测试tpp_simam.yaml配置文件
    print("\n2. 测试tpp_simam.yaml配置文件")
    try:
        tpp_config_path = project_root / "configs" / "tpp_simam.yaml"
        if tpp_config_path.exists():
            tpp_config = load_config(str(tpp_config_path))
            print(f"  ✅ tpp_simam.yaml加载成功")
            print(f"  - 模态: {tpp_config.data.modality}")
            print(f"  - enhanced_attention.enabled: {tpp_config.model.enhanced_attention.get('enabled')}")
            print(f"  - enhanced_attention.type: {tpp_config.model.enhanced_attention.get('type')}")
        else:
            print(f"  ❌ tpp_simam.yaml文件不存在: {tpp_config_path}")
    except Exception as e:
        print(f"  ❌ tpp_simam.yaml加载失败: {e}")


def main():
    """主函数"""
    print("脉冲星分类系统配置传递Bug修复验证")
    print("=" * 50)
    
    # 测试enhanced_attention配置传递
    results = test_enhanced_attention_config_passing()
    
    # 测试配置文件加载
    test_config_file_loading()
    
    # 总结验证结果
    print("\n=== 验证结果总结 ===")
    all_correct = all([
        results['fpp_ema_correct'],
        results['tpp_simam_correct'],
        results['baseline_correct']
    ])
    
    if all_correct:
        print("✅ 所有验证测试通过！配置传递Bug已成功修复。")
        print("✅ 模型参数数量符合预期，串联注意力架构正确实施。")
    else:
        print("❌ 部分验证测试失败，需要进一步检查。")
        if not results['fpp_ema_correct']:
            print("  - FPP+EMA模型参数数量不正确")
        if not results['tpp_simam_correct']:
            print("  - TPP+SimAM模型参数数量不正确")
        if not results['baseline_correct']:
            print("  - Baseline模型参数数量不正确")
    
    return all_correct


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
