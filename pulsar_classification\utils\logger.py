"""
日志记录系统
提供统一的日志记录接口，支持文件和控制台输出

特性：
- 多级别日志记录
- 文件和控制台同时输出
- 自动日志轮转
- 结构化日志格式
- TensorBoard集成
"""

import os
import logging
import sys
from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logger(
    name: str,
    log_dir: str = "logs",
    log_level: str = "INFO",
    console_output: bool = True,
    file_output: bool = True,
    max_bytes: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_dir: 日志文件目录
        log_level: 日志级别
        console_output: 是否输出到控制台
        file_output: 是否输出到文件
        max_bytes: 日志文件最大大小
        backup_count: 备份文件数量
        
    Returns:
        配置好的日志记录器
    """
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    colored_formatter = ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        console_handler.setFormatter(colored_formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if file_output:
        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)
        
        # 日志文件路径
        log_file = os.path.join(log_dir, f"{name}.log")
        
        # 使用RotatingFileHandler实现日志轮转
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, log_level.upper()))
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class ExperimentLogger:
    """
    实验日志记录器
    专门用于记录训练实验的详细信息
    """
    
    def __init__(
        self,
        experiment_name: str,
        log_dir: str = "logs",
        use_tensorboard: bool = True
    ):
        self.experiment_name = experiment_name
        self.log_dir = log_dir
        self.use_tensorboard = use_tensorboard
        
        # 创建实验目录
        self.experiment_dir = os.path.join(log_dir, experiment_name)
        os.makedirs(self.experiment_dir, exist_ok=True)

        # 创建标准化输出目录结构
        self.create_output_directories()
        
        # 设置基础日志记录器
        self.logger = setup_logger(
            name=experiment_name,
            log_dir=self.experiment_dir,
            log_level="INFO"
        )
        
        # TensorBoard记录器
        self.tb_writer = None
        if use_tensorboard:
            try:
                from torch.utils.tensorboard import SummaryWriter
                tb_dir = os.path.join(self.experiment_dir, "tensorboard")
                self.tb_writer = SummaryWriter(tb_dir)
                self.logger.info(f"TensorBoard logging enabled: {tb_dir}")
            except ImportError:
                self.logger.warning("TensorBoard not available. Install tensorboard to enable TB logging.")
        
        # 记录实验开始时间
        self.start_time = datetime.now()
        self.logger.info(f"Experiment '{experiment_name}' started at {self.start_time}")
    
    def log_config(self, config: Dict[str, Any]):
        """记录实验配置"""
        self.logger.info("Experiment Configuration:")
        self._log_dict(config, indent=1)
        
        # 保存配置到文件
        import json
        config_file = os.path.join(self.experiment_dir, "config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, default=str)
    
    def _log_dict(self, d: Dict[str, Any], indent: int = 0):
        """递归记录字典内容"""
        for key, value in d.items():
            if isinstance(value, dict):
                self.logger.info("  " * indent + f"{key}:")
                self._log_dict(value, indent + 1)
            else:
                self.logger.info("  " * indent + f"{key}: {value}")
    
    def log_metrics(self, metrics: Dict[str, float], step: int, prefix: str = ""):
        """记录指标"""
        # 控制台日志
        metric_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.logger.info(f"Step {step} - {prefix}{metric_str}")
        
        # TensorBoard日志
        if self.tb_writer:
            for key, value in metrics.items():
                tag = f"{prefix}{key}" if prefix else key
                self.tb_writer.add_scalar(tag, value, step)
    
    def log_model_info(self, model_info: Dict[str, Any]):
        """记录模型信息"""
        self.logger.info("Model Information:")
        self._log_dict(model_info, indent=1)
    
    def log_training_start(self, total_epochs: int, total_batches: int):
        """记录训练开始信息"""
        self.logger.info(f"Starting training: {total_epochs} epochs, {total_batches} batches per epoch")
    
    def log_epoch_summary(
        self,
        epoch: int,
        train_metrics: Dict[str, float],
        val_metrics: Dict[str, float],
        learning_rate: float,
        epoch_time: float
    ):
        """记录epoch总结"""
        self.logger.info(f"Epoch {epoch} Summary:")
        self.logger.info(f"  Training - {', '.join([f'{k}: {v:.4f}' for k, v in train_metrics.items()])}")
        self.logger.info(f"  Validation - {', '.join([f'{k}: {v:.4f}' for k, v in val_metrics.items()])}")
        self.logger.info(f"  Learning Rate: {learning_rate:.6f}")
        self.logger.info(f"  Epoch Time: {epoch_time:.2f}s")
        
        # TensorBoard记录
        if self.tb_writer:
            for key, value in train_metrics.items():
                self.tb_writer.add_scalar(f"train/{key}", value, epoch)
            for key, value in val_metrics.items():
                self.tb_writer.add_scalar(f"val/{key}", value, epoch)
            self.tb_writer.add_scalar("learning_rate", learning_rate, epoch)
            self.tb_writer.add_scalar("epoch_time", epoch_time, epoch)
    
    def log_best_model(self, epoch: int, metrics: Dict[str, float]):
        """记录最佳模型信息"""
        self.logger.info(f"New best model at epoch {epoch}:")
        self._log_dict(metrics, indent=1)
    
    def log_early_stopping(self, epoch: int, patience: int):
        """记录早停信息"""
        self.logger.info(f"Early stopping triggered at epoch {epoch} (patience: {patience})")
    
    def log_training_complete(self, total_time: float, best_metrics: Dict[str, float]):
        """记录训练完成信息"""
        self.logger.info(f"Training completed in {total_time:.2f}s")
        self.logger.info("Best model metrics:")
        self._log_dict(best_metrics, indent=1)
        
        # 记录实验结束时间
        end_time = datetime.now()
        duration = end_time - self.start_time
        self.logger.info(f"Experiment completed at {end_time} (duration: {duration})")
    
    def create_output_directories(self):
        """创建标准化输出目录结构"""
        dirs = ['metrics', 'visualizations', 'analysis']
        for dir_name in dirs:
            dir_path = os.path.join(self.experiment_dir, dir_name)
            os.makedirs(dir_path, exist_ok=True)

    def save_confusion_matrix(self, cm, class_names, epoch):
        """保存混淆矩阵可视化"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            import numpy as np

            plt.figure(figsize=(8, 6))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=class_names, yticklabels=class_names)
            plt.title(f'Confusion Matrix - Epoch {epoch}')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')

            save_path = os.path.join(self.experiment_dir, 'visualizations',
                                   f'confusion_matrix_epoch_{epoch}.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            self.logger.info(f"Confusion matrix saved to: {save_path}")
        except ImportError:
            self.logger.warning("matplotlib/seaborn not available for confusion matrix visualization")

    def save_metrics_json(self, metrics, filename):
        """保存指标到JSON文件"""
        import json
        save_path = os.path.join(self.experiment_dir, 'metrics', filename)
        with open(save_path, 'w') as f:
            json.dump(metrics, f, indent=2, default=str)
        self.logger.info(f"Metrics saved to: {save_path}")

    def save_training_history(self, history):
        """保存训练历史"""
        import json
        save_path = os.path.join(self.experiment_dir, 'metrics', 'training_history.json')
        with open(save_path, 'w') as f:
            json.dump(history, f, indent=2, default=str)
        self.logger.info(f"Training history saved to: {save_path}")

    def save_misclassified_analysis(self, misclassified_data, filename='misclassified_analysis.txt'):
        """保存误分类样本分析"""
        save_path = os.path.join(self.experiment_dir, 'analysis', filename)
        with open(save_path, 'w') as f:
            f.write("Misclassified Samples Analysis\n")
            f.write("=" * 50 + "\n\n")
            for item in misclassified_data:
                f.write(f"Sample Path: {item['path']}\n")
                f.write(f"True Label: {item['true_label']}\n")
                f.write(f"Predicted Label: {item['pred_label']}\n")
                f.write(f"Prediction Confidence: {item['confidence']:.4f}\n")
                f.write("-" * 30 + "\n")
        self.logger.info(f"Misclassified analysis saved to: {save_path}")

    def close(self):
        """关闭日志记录器"""
        if self.tb_writer:
            self.tb_writer.close()

        # 关闭文件处理器
        for handler in self.logger.handlers:
            if isinstance(handler, logging.FileHandler):
                handler.close()


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return logging.getLogger(name)


# 导出的公共接口
__all__ = [
    'setup_logger',
    'ExperimentLogger',
    'get_logger'
]
